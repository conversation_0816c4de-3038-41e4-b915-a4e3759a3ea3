require('dotenv').config();
const mongoose = require('mongoose');

// Define the Visit schema (same as in models)
const visitSchema = new mongoose.Schema({
  ip: { type: String, required: true },
  section: { type: String, required: true },
  duration: { type: Number, default: 0 },
  timestamp: { type: Date, default: Date.now },
  sessionId: String,
  pageUrl: String,
  userAgent: String,
  referrer: String,
  jobTitle: String,
  jobSlug: String,
  projectTitle: String,
  interactionType: String
});

const Visit = mongoose.model('Visit', visitSchema);

async function checkDatabase() {
  console.log('🔍 Checking database state...');
  
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Get total visits
    const totalVisits = await Visit.countDocuments();
    console.log(`📊 Total visits in database: ${totalVisits}`);
    
    // Get unique IPs
    const uniqueIPs = await Visit.distinct('ip');
    console.log(`👥 Unique visitor IPs: ${uniqueIPs.length}`);
    
    // Show sample IPs
    console.log(`🌍 Sample IPs: ${uniqueIPs.slice(0, 5).join(', ')}`);
    
    // Check for comma-separated IPs (should be none after cleanup)
    const commaIPs = await Visit.find({ ip: { $regex: ',' } });
    console.log(`🔧 Visits with comma-separated IPs: ${commaIPs.length}`);
    
    if (commaIPs.length > 0) {
      console.log('⚠️ Found comma-separated IPs:');
      commaIPs.slice(0, 3).forEach(visit => {
        console.log(`   - ${visit.ip} (${visit.section})`);
      });
    }
    
    // Get recent visits
    const recentVisits = await Visit.find().sort({ timestamp: -1 }).limit(5);
    console.log('\n📅 Recent visits:');
    recentVisits.forEach(visit => {
      console.log(`   - ${visit.ip} visited ${visit.section} for ${visit.duration}s at ${visit.timestamp.toISOString()}`);
    });
    
    // Test geolocation for a few IPs
    console.log('\n🌍 Testing geolocation for sample IPs...');
    const testIPs = uniqueIPs.slice(0, 2); // Test first 2 unique IPs
    
    for (const ip of testIPs) {
      await testGeolocation(ip);
    }
    
    console.log('\n✅ Database check complete!');
    
  } catch (error) {
    console.error('❌ Database check error:', error);
  } finally {
    await mongoose.disconnect();
  }
}

async function testGeolocation(ip) {
  try {
    const https = require('https');
    
    return new Promise((resolve) => {
      const req = https.request(`https://ipapi.co/${ip}/json/`, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const geoData = JSON.parse(data);
            if (geoData.error) {
              console.log(`   ⚠️ ${ip}: ${geoData.reason}`);
            } else {
              console.log(`   ✅ ${ip}: ${geoData.country_name}, ${geoData.city}`);
            }
          } catch (error) {
            console.log(`   ❌ ${ip}: Parse error - ${error.message}`);
          }
          resolve();
        });
      });
      
      req.on('error', (error) => {
        console.log(`   ❌ ${ip}: Request error - ${error.message}`);
        resolve();
      });
      
      req.setTimeout(5000, () => {
        console.log(`   ⏰ ${ip}: Timeout`);
        req.destroy();
        resolve();
      });
      
      req.end();
    });
  } catch (error) {
    console.log(`   ❌ ${ip}: ${error.message}`);
  }
}

checkDatabase();
