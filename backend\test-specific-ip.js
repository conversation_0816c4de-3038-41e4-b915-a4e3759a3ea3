require('dotenv').config();
const { batchGetCountriesFromIPs } = require('./utils/geolocation');

async function testSpecificIP() {
  console.log('🎯 TESTING SPECIFIC IP: **************');
  console.log('=====================================\n');
  
  const testIP = '**************';
  
  console.log(`🔍 Testing IP: ${testIP}`);
  console.log('This IP was previously showing as "Unknown"');
  console.log('After expanding IP ranges, it should now show as Tunisia 🇹🇳\n');
  
  try {
    const results = await batchGetCountriesFromIPs([testIP]);
    const result = results[testIP];
    
    console.log('📊 RESULT:');
    console.log('==========');
    
    if (result.error || result.country === 'Unknown') {
      console.log(`❌ STILL UNKNOWN: ${testIP}`);
      console.log(`   Country: ${result.country}`);
      console.log(`   Error: ${result.errorMessage || 'No error message'}`);
      console.log(`   Flag: ${result.flag}`);
      console.log('\n🔧 DIAGNOSIS: The IP range 102.157.x.x is still not covered!');
      console.log('   Need to add this specific range to the heuristics.');
    } else {
      console.log(`✅ SUCCESS: ${testIP}`);
      console.log(`   Country: ${result.country} ${result.flag}`);
      console.log(`   City: ${result.city}`);
      console.log(`   ISP: ${result.isp}`);
      console.log(`   Timezone: ${result.timezone}`);
      console.log('\n🎉 The IP is now correctly identified as Tunisia!');
    }
    
    // Test a few more similar IPs
    console.log('\n🧪 Testing similar IP patterns:');
    console.log('===============================');
    
    const similarIPs = [
      '***********',
      '*************',
      '***************',
      '***************',
      '***********',
      '***********',
      '***********'
    ];
    
    const similarResults = await batchGetCountriesFromIPs(similarIPs);
    
    let successCount = 0;
    Object.entries(similarResults).forEach(([ip, data]) => {
      const status = (data.error || data.country === 'Unknown') ? '❌' : '✅';
      const location = (data.error || data.country === 'Unknown') 
        ? 'UNKNOWN' 
        : `${data.country} ${data.flag}`;
      
      console.log(`${status} ${ip.padEnd(17)} → ${location}`);
      
      if (!data.error && data.country !== 'Unknown') {
        successCount++;
      }
    });
    
    console.log(`\n📈 Success Rate: ${successCount}/${similarIPs.length} (${((successCount/similarIPs.length)*100).toFixed(1)}%)`);
    
    if (successCount === similarIPs.length) {
      console.log('🎉 ALL SIMILAR IPs NOW IDENTIFIED CORRECTLY!');
    } else {
      console.log('⚠️ Some IPs still showing as Unknown - may need more ranges.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSpecificIP();
