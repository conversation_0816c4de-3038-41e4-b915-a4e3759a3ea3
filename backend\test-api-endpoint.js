require('dotenv').config();

async function testGeolocationEndpoint() {
  const fetch = (await import('node-fetch')).default;
  console.log('🧪 TESTING GEOLOCATION API ENDPOINT');
  console.log('===================================\n');
  
  try {
    // First, login to get a token
    console.log('1️⃣ Logging in to get auth token...');
    const loginResponse = await fetch('http://localhost:5000/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Adminboss'
      })
    });
    
    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }
    
    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ Login successful, got token');
    
    // Test the geolocation endpoint
    console.log('\n2️⃣ Testing geolocation endpoint...');
    const testIPs = ['*******', '127.0.0.1', '***********'];
    
    const geoResponse = await fetch('http://localhost:5000/api/admin/geolocation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        ips: testIPs
      })
    });
    
    if (!geoResponse.ok) {
      throw new Error(`Geolocation request failed: ${geoResponse.status}`);
    }
    
    const geoData = await geoResponse.json();
    console.log('✅ Geolocation endpoint response:', JSON.stringify(geoData, null, 2));
    
    // Verify the response structure
    if (geoData.success && geoData.data) {
      console.log('\n📊 RESULTS SUMMARY:');
      Object.entries(geoData.data).forEach(([ip, data]) => {
        const status = data.error ? '❌' : '✅';
        const location = data.error 
          ? `Error: ${data.errorMessage}` 
          : `${data.country}, ${data.city} ${data.flag}`;
        
        console.log(`${status} ${ip.padEnd(15)} → ${location}`);
      });
      
      console.log('\n🎉 Geolocation API endpoint is working correctly!');
    } else {
      console.log('⚠️ Unexpected response structure');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testGeolocationEndpoint();
