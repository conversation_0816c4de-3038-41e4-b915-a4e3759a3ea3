/**
 * Simple test script to verify the backend ping endpoint
 * Run with: node test-ping.js
 */

const fetch = require('node-fetch');

const BACKEND_URL = 'https://porfolio-pro-backend.onrender.com'; // Production URL
const PING_URL = `${BACKEND_URL}/api/ping`;

async function testPing() {
  console.log('🧪 Testing backend ping endpoint...');
  console.log('URL:', PING_URL);
  
  try {
    const startTime = Date.now();
    const response = await fetch(PING_URL);
    const duration = Date.now() - startTime;
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`⏱️ Response Time: ${duration}ms`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success! Response:', data);
    } else {
      console.log('❌ Failed with status:', response.status);
      const text = await response.text();
      console.log('Response body:', text);
    }
  } catch (error) {
    console.log('💥 Error:', error.message);
  }
}

// Test the root endpoint too
async function testRoot() {
  console.log('\n🧪 Testing root endpoint...');
  const rootUrl = `${BACKEND_URL}/`;
  
  try {
    const response = await fetch(rootUrl);
    console.log(`📊 Root Status: ${response.status}`);
    
    if (response.ok) {
      const text = await response.text();
      console.log('✅ Root Response:', text);
    }
  } catch (error) {
    console.log('💥 Root Error:', error.message);
  }
}

async function runTests() {
  await testRoot();
  await testPing();
}

runTests();
