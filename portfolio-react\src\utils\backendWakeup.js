/**
 * Backend Wakeup Utility
 *
 * This utility automatically pings the backend when the frontend loads
 * to prevent cold start delays on Render's free tier.
 */

import { logUserFriendly, logSensitive, warnUserFriendly, warnSensitive } from './logger';

const API_URL = process.env.REACT_APP_API_URL;
const PING_ENDPOINT = `${API_URL}/api/ping`;
const PING_TIMEOUT = 10000; // 10 seconds timeout

/**
 * Pings the backend to wake it up from sleep mode
 * @returns {Promise<boolean>} - Returns true if successful, false otherwise
 */
export const wakeupBackend = async () => {
  // Don't ping if no API URL is configured
  if (!API_URL) {
    warnSensitive('Backend wakeup: No API URL configured');
    return false;
  }

  // This message should always be visible to users
  logUserFriendly('🚀 Waking up backend server...');
  const startTime = Date.now();

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), PING_TIMEOUT);

    const response = await fetch(PING_ENDPOINT, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      const duration = Date.now() - startTime;

      // This success message should always be visible to users
      logUserFriendly(`✅ Backend is awake! Response time: ${duration}ms`);
      // Backend response details are sensitive - only show in development
      logSensitive('Backend response:', data);

      return true;
    } else {
      warnUserFriendly(`⚠️ Backend ping failed with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    const duration = Date.now() - startTime;

    if (error.name === 'AbortError') {
      warnUserFriendly(`⏰ Backend ping timed out after ${duration}ms`);
    } else {
      warnUserFriendly('❌ Backend ping failed:', error.message);
    }

    return false;
  }
};

/**
 * Initializes backend wakeup on app load
 * This function should be called once when the app starts
 */
export const initializeBackendWakeup = () => {
  // Use setTimeout to ensure this doesn't block initial rendering
  setTimeout(() => {
    wakeupBackend().catch(error => {
      warnUserFriendly('Backend wakeup initialization failed:', error.message);
    });
  }, 100); // Small delay to not block rendering
};

/**
 * Preemptive wakeup for user interactions
 * Call this before making actual API calls to ensure backend is ready
 */
export const preemptiveWakeup = async () => {
  // Only wake up if it's been more than 5 minutes since last ping
  const lastPing = localStorage.getItem('lastBackendPing');
  const now = Date.now();
  const fiveMinutes = 5 * 60 * 1000;

  if (!lastPing || (now - parseInt(lastPing)) > fiveMinutes) {
    const success = await wakeupBackend();
    if (success) {
      localStorage.setItem('lastBackendPing', now.toString());
    }
    return success;
  }

  return true; // Assume backend is still awake
};

const backendWakeupUtils = {
  wakeupBackend,
  initializeBackendWakeup,
  preemptiveWakeup,
};

export default backendWakeupUtils;
