<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Flag Display Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .loading { color: #ff9800; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .visitor-card {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 15px;
            border: 1px solid #555;
        }
        .country-flag {
            font-size: 2rem;
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            border: 2px solid #666;
            padding: 5px;
            border-radius: 4px;
            background: #444;
        }
        .flag-debug {
            font-family: monospace;
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }
        .visitor-info {
            flex: 1;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
    </style>
</head>
<body>
    <h1>🐛 Debug Flag Display Issue</h1>
    <p>This page will help identify why country flags are not showing in the visitor cards.</p>

    <div class="test-section">
        <h2>1. Test Flag Generation Functions</h2>
        <button onclick="testFlagGeneration()">Test Flag Generation</button>
        <div id="flag-generation-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Backend Geolocation Response</h2>
        <button onclick="testBackendGeoResponse()">Test Backend Response</button>
        <div id="backend-geo-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Simulate Visitor Card Display</h2>
        <button onclick="simulateVisitorCards()">Simulate Visitor Cards</button>
        <div id="visitor-cards-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Specific Problem IP</h2>
        <button onclick="testSpecificIP()">Test **************</button>
        <div id="specific-ip-result"></div>
    </div>

    <script>
        const API_URL = 'https://porfolio-pro-backend.onrender.com';

        // Flag generation function (same as in React component)
        function getCountryFlag(countryCode) {
            if (!countryCode || countryCode === 'UN' || countryCode === 'LO') {
                return '🌍';
            }
            
            try {
                const codePoints = countryCode
                    .toUpperCase()
                    .split('')
                    .map(char => 127397 + char.charCodeAt());
                
                return String.fromCodePoint(...codePoints);
            } catch (error) {
                console.error('Flag generation error:', error);
                return '🌍';
            }
        }

        // Visitor flag function (same as in React component)
        function getVisitorFlag(visitor) {
            // First try: use existing flag from geo data
            if (visitor.geo?.flag && visitor.geo.flag !== '⏳') return visitor.geo.flag;

            // Second try: generate flag from country_code
            if (visitor.geo?.country_code && visitor.geo.country_code !== 'UN') {
                return getCountryFlag(visitor.geo.country_code);
            }

            // Third try: map common country names to codes
            const countryToCode = {
                'Tunisia': 'TN',
                'France': 'FR',
                'United States': 'US',
                'Germany': 'DE',
                'United Kingdom': 'GB',
                'Canada': 'CA',
                'Japan': 'JP',
                'Australia': 'AU',
                'Brazil': 'BR',
                'India': 'IN',
                'Local': 'LO',
                'Unknown': 'UN'
            };

            if (visitor.geo?.country && countryToCode[visitor.geo.country]) {
                return getCountryFlag(countryToCode[visitor.geo.country]);
            }

            // Special handling for loading state
            if (visitor.geo?.country === 'Loading...') {
                return '⏳';
            }

            // Default fallback
            return '🌍';
        }

        async function getAuthToken() {
            let token = localStorage.getItem('token');
            if (!token) {
                try {
                    const response = await fetch(`${API_URL}/api/admin/login`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'Adminboss'
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        token = data.token;
                        localStorage.setItem('token', token);
                    }
                } catch (error) {
                    console.error('Login error:', error);
                }
            }
            return token;
        }

        function testFlagGeneration() {
            const resultDiv = document.getElementById('flag-generation-result');
            
            const testCodes = ['TN', 'FR', 'US', 'DE', 'GB', 'CA', 'JP', 'AU', 'UN', 'LO'];
            
            let resultHTML = '<div class="success">✅ Testing flag generation:</div>';
            
            testCodes.forEach(code => {
                const flag = getCountryFlag(code);
                resultHTML += `
                    <div style="margin: 10px 0; padding: 10px; background: #333; border-radius: 4px;">
                        <span class="country-flag">${flag}</span>
                        <span style="margin-left: 15px;">Code: ${code} → Flag: ${flag}</span>
                        <div class="flag-debug">Unicode: ${flag.codePointAt ? Array.from(flag).map(c => c.codePointAt(0)).join(', ') : 'N/A'}</div>
                    </div>
                `;
            });
            
            resultDiv.innerHTML = resultHTML;
        }

        async function testBackendGeoResponse() {
            const resultDiv = document.getElementById('backend-geo-result');
            resultDiv.innerHTML = '<div class="loading">Testing backend geolocation response...</div>';
            
            try {
                const token = await getAuthToken();
                
                const testIPs = ['**************', '*************', '*************', '*******'];
                
                const response = await fetch(`${API_URL}/api/admin/geolocation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ ips: testIPs })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                let resultHTML = '<div class="success">✅ Backend geolocation response:</div>';
                
                Object.entries(data.data).forEach(([ip, geo]) => {
                    const hasFlag = geo.flag && geo.flag !== '🌍';
                    const flagStatus = hasFlag ? '✅' : '❌';
                    
                    resultHTML += `
                        <div style="margin: 10px 0; padding: 10px; background: #333; border-radius: 4px;">
                            <div><strong>IP:</strong> ${ip}</div>
                            <div><strong>Country:</strong> ${geo.country}</div>
                            <div><strong>Country Code:</strong> ${geo.country_code}</div>
                            <div><strong>Flag:</strong> <span class="country-flag">${geo.flag}</span> ${flagStatus}</div>
                            <div><strong>Error:</strong> ${geo.error ? 'Yes' : 'No'}</div>
                            ${geo.errorMessage ? `<div><strong>Error Message:</strong> ${geo.errorMessage}</div>` : ''}
                        </div>
                    `;
                });
                
                resultDiv.innerHTML = resultHTML;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Backend test failed!</div>
                    <div class="error">Error: ${error.message}</div>
                `;
            }
        }

        function simulateVisitorCards() {
            const resultDiv = document.getElementById('visitor-cards-result');
            
            // Simulate visitor data as it would come from the backend
            const mockVisitors = [
                {
                    ip: '**************',
                    geo: { country: 'Tunisia', country_code: 'TN', city: 'Tunis', flag: '🇹🇳', error: false }
                },
                {
                    ip: '*************',
                    geo: { country: 'Tunisia', country_code: 'TN', city: 'Tunis', flag: '🇹🇳', error: false }
                },
                {
                    ip: '*************',
                    geo: { country: 'Tunisia', country_code: 'TN', city: 'Tunis', flag: '🇹🇳', error: false }
                },
                {
                    ip: '*******',
                    geo: { country: 'United States', country_code: 'US', city: 'Mountain View', flag: '🇺🇸', error: false }
                },
                {
                    ip: '127.0.0.1',
                    geo: { country: 'Local', country_code: 'LO', city: 'Localhost', flag: '🏠', error: false }
                },
                {
                    ip: '*******',
                    geo: { country: 'Unknown', country_code: 'UN', city: 'Unknown', flag: '🌍', error: true }
                }
            ];
            
            let resultHTML = '<div class="success">✅ Simulated visitor cards:</div><div class="test-grid">';
            
            mockVisitors.forEach(visitor => {
                const displayFlag = getVisitorFlag(visitor);
                
                resultHTML += `
                    <div class="visitor-card">
                        <span class="country-flag">${displayFlag}</span>
                        <div class="visitor-info">
                            <div><strong>${visitor.ip}</strong></div>
                            <div>${visitor.geo.country}, ${visitor.geo.city}</div>
                            <div class="flag-debug">
                                Raw flag: ${visitor.geo.flag}<br>
                                Computed flag: ${displayFlag}<br>
                                Country code: ${visitor.geo.country_code}<br>
                                Error: ${visitor.geo.error}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultHTML += '</div>';
            resultDiv.innerHTML = resultHTML;
        }

        async function testSpecificIP() {
            const resultDiv = document.getElementById('specific-ip-result');
            resultDiv.innerHTML = '<div class="loading">Testing specific IP **************...</div>';
            
            try {
                const token = await getAuthToken();
                
                const response = await fetch(`${API_URL}/api/admin/geolocation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ ips: ['**************'] })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const geo = data.data['**************'];
                
                const mockVisitor = {
                    ip: '**************',
                    geo: geo
                };
                
                const displayFlag = getVisitorFlag(mockVisitor);
                
                let resultHTML = `
                    <div class="success">✅ Specific IP test results:</div>
                    <div class="visitor-card">
                        <span class="country-flag">${displayFlag}</span>
                        <div class="visitor-info">
                            <div><strong>**************</strong></div>
                            <div>${geo.country}, ${geo.city}</div>
                            <div class="flag-debug">
                                Backend flag: ${geo.flag}<br>
                                Display flag: ${displayFlag}<br>
                                Country: ${geo.country}<br>
                                Country code: ${geo.country_code}<br>
                                Error: ${geo.error}<br>
                                ${geo.errorMessage ? `Error message: ${geo.errorMessage}` : ''}
                            </div>
                        </div>
                    </div>
                `;
                
                if (geo.country === 'Unknown' || geo.error) {
                    resultHTML += '<div class="error">❌ IP still showing as Unknown! Check backend IP ranges.</div>';
                } else if (displayFlag === '🌍') {
                    resultHTML += '<div class="warning">⚠️ Flag generation failed! Check flag logic.</div>';
                } else {
                    resultHTML += '<div class="success">🎉 IP correctly identified with flag!</div>';
                }
                
                resultDiv.innerHTML = resultHTML;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Specific IP test failed!</div>
                    <div class="error">Error: ${error.message}</div>
                `;
            }
        }

        // Auto-run flag generation test on page load
        window.onload = () => {
            testFlagGeneration();
        };
    </script>
</body>
</html>
