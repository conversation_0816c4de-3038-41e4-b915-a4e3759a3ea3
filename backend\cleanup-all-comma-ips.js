require('dotenv').config();
const mongoose = require('mongoose');

// Define the Visit schema
const visitSchema = new mongoose.Schema({
  ip: { type: String, required: true },
  section: { type: String, required: true },
  duration: { type: Number, default: 0 },
  timestamp: { type: Date, default: Date.now },
  sessionId: String,
  pageUrl: String,
  userAgent: String,
  referrer: String,
  jobTitle: String,
  jobSlug: String,
  projectTitle: String,
  interactionType: String
});

const Visit = mongoose.model('Visit', visitSchema);

async function cleanupAllCommaIPs() {
  console.log('🧹 Cleaning up ALL comma-separated IPs...');
  
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Find all visits with comma-separated IPs
    const commaVisits = await Visit.find({ ip: { $regex: ',' } });
    console.log(`🔍 Found ${commaVisits.length} visits with comma-separated IPs`);
    
    if (commaVisits.length === 0) {
      console.log('✅ No comma-separated IPs found!');
      return;
    }
    
    // Show details of comma-separated IPs
    console.log('\n📋 Details of comma-separated IPs:');
    commaVisits.forEach((visit, index) => {
      console.log(`${index + 1}. IP: "${visit.ip}"`);
      console.log(`   Section: ${visit.section}`);
      console.log(`   Timestamp: ${visit.timestamp.toISOString()}`);
      console.log(`   Session: ${visit.sessionId || 'N/A'}`);
      console.log('');
    });
    
    // Process each visit
    let processedCount = 0;
    let errorCount = 0;
    
    for (const visit of commaVisits) {
      try {
        // Extract the first IP from comma-separated string
        const originalIP = visit.ip;
        const firstIP = originalIP.split(',')[0].trim();
        
        console.log(`🔧 Processing: "${originalIP}" → "${firstIP}"`);
        
        // Update the visit with the first IP
        await Visit.updateOne(
          { _id: visit._id },
          { $set: { ip: firstIP } }
        );
        
        processedCount++;
        
      } catch (error) {
        console.error(`❌ Error processing visit ${visit._id}:`, error.message);
        errorCount++;
      }
    }
    
    console.log(`\n📊 Cleanup Summary:`);
    console.log(`✅ Successfully processed: ${processedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log(`📈 Total visits processed: ${commaVisits.length}`);
    
    // Verify cleanup
    console.log('\n🔍 Verifying cleanup...');
    const remainingCommaVisits = await Visit.find({ ip: { $regex: ',' } });
    console.log(`📊 Remaining comma-separated IPs: ${remainingCommaVisits.length}`);
    
    if (remainingCommaVisits.length === 0) {
      console.log('🎉 All comma-separated IPs have been cleaned up!');
    } else {
      console.log('⚠️ Some comma-separated IPs still remain:');
      remainingCommaVisits.forEach(visit => {
        console.log(`   - "${visit.ip}" (${visit.section})`);
      });
    }
    
    // Show final statistics
    const totalVisits = await Visit.countDocuments();
    const uniqueIPs = await Visit.distinct('ip');
    console.log(`\n📈 Final Database Statistics:`);
    console.log(`📊 Total visits: ${totalVisits}`);
    console.log(`👥 Unique IPs: ${uniqueIPs.length}`);
    console.log(`🌍 Sample IPs: ${uniqueIPs.slice(0, 5).join(', ')}`);
    
  } catch (error) {
    console.error('❌ Cleanup error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

cleanupAllCommaIPs();
