require('dotenv').config();
const { batchGetCountriesFromIPs } = require('./backend/utils/geolocation');

async function testBackendGeolocationFix() {
  console.log('🧪 TESTING BACKEND GEOLOCATION FIX');
  console.log('==================================\n');
  
  // Test with real IPs from your visitor logs
  const testIPs = [
    '*************',     // Tunisia
    '**************',    // Tunisia
    '*************',     // Tunisia
    '**************',    // Tunisia
    '**************',    // Tunisia
    '************',      // Tunisia
    '***************',   // Tunisia
    '***************',   // France/Europe
    '127.0.0.1',         // Local
    '*******'            // US DNS
  ];
  
  console.log('🎯 Testing with real visitor IPs from your logs:');
  testIPs.forEach(ip => console.log(`   - ${ip}`));
  console.log('\n🚀 Starting geolocation lookup...\n');
  
  try {
    const results = await batchGetCountriesFromIPs(testIPs);
    
    console.log('\n📊 RESULTS:');
    console.log('===========');
    
    let tunisiaCount = 0;
    let unknownCount = 0;
    let totalCount = 0;
    
    Object.entries(results).forEach(([ip, data]) => {
      const status = data.error ? '❌' : '✅';
      const location = data.error 
        ? `${data.country} (${data.errorMessage})` 
        : `${data.country}, ${data.city} ${data.flag}`;
      
      console.log(`${status} ${ip.padEnd(17)} → ${location}`);
      
      totalCount++;
      if (data.country === 'Tunisia') tunisiaCount++;
      if (data.country === 'Unknown') unknownCount++;
    });
    
    console.log('\n📈 SUMMARY:');
    console.log('===========');
    console.log(`✅ Total IPs processed: ${totalCount}`);
    console.log(`🇹🇳 Tunisia IPs identified: ${tunisiaCount}`);
    console.log(`❓ Unknown IPs: ${unknownCount}`);
    console.log(`📊 Tunisia detection rate: ${((tunisiaCount / totalCount) * 100).toFixed(1)}%`);
    
    if (tunisiaCount >= 5) {
      console.log('\n🎉 SUCCESS: Tunisia IP detection is working correctly!');
      console.log('   Most of your visitor IPs should now show as Tunisia instead of Unknown.');
    } else {
      console.log('\n⚠️ WARNING: Low Tunisia detection rate. May need to add more IP ranges.');
    }
    
    if (unknownCount === 0) {
      console.log('✅ No unknown locations - all IPs have been identified!');
    } else {
      console.log(`⚠️ ${unknownCount} IPs still showing as Unknown - consider adding more heuristics.`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testBackendGeolocationFix();
