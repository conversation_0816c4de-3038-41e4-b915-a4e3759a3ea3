// Simple test to check if environment variable is loaded
console.log('Current working directory:', process.cwd());
console.log('Environment file exists:', require('fs').existsSync('.env'));

if (require('fs').existsSync('.env')) {
  const envContent = require('fs').readFileSync('.env', 'utf8');
  console.log('Environment file content:');
  console.log(envContent);
}

// Load dotenv
require('dotenv').config();
console.log('REACT_APP_API_URL from process.env:', process.env.REACT_APP_API_URL);
