// Simple test script to verify analytics functionality
const API_URL = 'http://localhost:5000';

// Test function to simulate visitor tracking
async function testVisitorTracking() {
  console.log('Testing visitor tracking...');
  
  try {
    // Simulate a visit to the intro section
    const visitData = {
      section: 'intro',
      duration: 5,
      userAgent: 'Test Browser',
      sessionId: 'test_session_123',
      pageUrl: 'http://localhost:3001/'
    };

    const response = await fetch(`${API_URL}/api/track/visit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(visitData)
    });

    if (response.ok) {
      console.log('✅ Visit tracking successful');
    } else {
      console.log('❌ Visit tracking failed:', response.status);
    }
  } catch (error) {
    console.log('❌ Visit tracking error:', error.message);
  }
}

// Test function to check admin dashboard data
async function testAdminDashboard() {
  console.log('Testing admin dashboard...');
  
  try {
    // First, try to login (you'll need to replace with actual credentials)
    const loginResponse = await fetch(`${API_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>', // Replace with actual admin email
        password: 'admin123' // Replace with actual admin password
      })
    });

    if (loginResponse.ok) {
      const { token } = await loginResponse.json();
      console.log('✅ Admin login successful');

      // Test dashboard data
      const dashboardResponse = await fetch(`${API_URL}/api/admin/dashboard`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json();
        console.log('✅ Dashboard data retrieved:', dashboardData);
      } else {
        console.log('❌ Dashboard data failed:', dashboardResponse.status);
      }

      // Test section details
      const sectionDetailsResponse = await fetch(`${API_URL}/api/track/section-details`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (sectionDetailsResponse.ok) {
        const sectionDetails = await sectionDetailsResponse.json();
        console.log('✅ Section details retrieved:', sectionDetails);
      } else {
        console.log('❌ Section details failed:', sectionDetailsResponse.status);
      }
    } else {
      console.log('❌ Admin login failed:', loginResponse.status);
    }
  } catch (error) {
    console.log('❌ Admin dashboard error:', error.message);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting analytics tests...\n');
  
  await testVisitorTracking();
  console.log('');
  await testAdminDashboard();
  
  console.log('\n✨ Tests completed!');
}

// Run the tests if this script is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  runTests();
} else {
  // Browser environment
  window.runAnalyticsTests = runTests;
  console.log('Analytics test functions loaded. Run window.runAnalyticsTests() to test.');
}
