const API_URL = 'http://localhost:5000';

async function testAPI() {
  const fetch = (await import('node-fetch')).default;
  console.log('🚀 Testing Backend API...');
  
  try {
    // Test 1: Basic API health check
    console.log('\n📍 Test 1: API Health Check');
    const healthResponse = await fetch(`${API_URL}/`);
    const healthText = await healthResponse.text();
    console.log(`✅ Health check: ${healthText}`);
    
    // Test 2: Create test visits with different sections
    console.log('\n📍 Test 2: Creating Test Visits');
    const testVisits = [
      { section: 'hero', duration: 15, sessionId: 'test-session-1' },
      { section: 'about', duration: 25, sessionId: 'test-session-2' },
      { section: 'portfolio', duration: 45, sessionId: 'test-session-3' },
      { section: 'contact', duration: 12, sessionId: 'test-session-4' },
      { section: 'job-detail-hero-test', duration: 20, sessionId: 'test-session-5', jobTitle: '3D E-commerce Platform UI/UX Designer', jobSlug: '3d-ecommerce-platform' },
      { section: 'project-card-view', duration: 8, sessionId: 'test-session-6', projectTitle: '3D Product Visualization Engine', interactionType: 'view' }
    ];
    
    for (const visit of testVisits) {
      try {
        const response = await fetch(`${API_URL}/api/track/visit`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(visit)
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log(`✅ Visit logged: ${visit.section} (${visit.duration}s) - ${result.message}`);
        } else {
          console.log(`❌ Failed to log visit: ${visit.section} - Status: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Error logging visit ${visit.section}: ${error.message}`);
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Test 3: Check if we need admin token for dashboard
    console.log('\n📍 Test 3: Testing Dashboard Access (should fail without token)');
    try {
      const dashboardResponse = await fetch(`${API_URL}/api/admin/dashboard`);
      console.log(`Dashboard response status: ${dashboardResponse.status}`);
      if (dashboardResponse.status === 401) {
        console.log('✅ Dashboard properly protected (401 Unauthorized)');
      } else {
        console.log('⚠️ Dashboard access unexpected status');
      }
    } catch (error) {
      console.log(`❌ Dashboard test error: ${error.message}`);
    }
    
    // Test 4: Test admin login
    console.log('\n📍 Test 4: Testing Admin Login');
    try {
      const loginResponse = await fetch(`${API_URL}/api/admin/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'Adminboss'
        })
      });
      
      if (loginResponse.ok) {
        const loginResult = await loginResponse.json();
        console.log('✅ Admin login successful');
        
        // Test 5: Test dashboard with token
        console.log('\n📍 Test 5: Testing Dashboard with Token');
        const dashboardWithTokenResponse = await fetch(`${API_URL}/api/admin/dashboard`, {
          headers: {
            'Authorization': `Bearer ${loginResult.token}`
          }
        });
        
        if (dashboardWithTokenResponse.ok) {
          const dashboardData = await dashboardWithTokenResponse.json();
          console.log(`✅ Dashboard data retrieved: ${dashboardData.totalVisits} total visits`);
          console.log(`📊 Unique visitors: ${dashboardData.visits.length > 0 ? new Set(dashboardData.visits.map(v => v.ip)).size : 0}`);
          
          // Show some sample IPs for geolocation testing
          if (dashboardData.visits.length > 0) {
            const uniqueIPs = [...new Set(dashboardData.visits.map(v => v.ip))];
            console.log(`🌍 Sample IPs for geolocation testing: ${uniqueIPs.slice(0, 3).join(', ')}`);
          }
        } else {
          console.log(`❌ Dashboard with token failed: ${dashboardWithTokenResponse.status}`);
        }
      } else {
        console.log(`❌ Admin login failed: ${loginResponse.status}`);
      }
    } catch (error) {
      console.log(`❌ Login test error: ${error.message}`);
    }
    
    console.log('\n🎉 API Testing Complete!');
    console.log('\n📝 Next Steps:');
    console.log('1. Open http://localhost:3000/test-geolocation.html to test geolocation');
    console.log('2. Open http://localhost:3000/test-job-detail-tracking.html to test job tracking');
    console.log('3. Login to admin dashboard and check all-visitors page');
    
  } catch (error) {
    console.error('❌ API Test failed:', error);
  }
}

// Run the test
testAPI();
