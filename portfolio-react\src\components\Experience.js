import React from 'react';
import { Link } from 'react-router-dom';
import { jobsData } from '../data/jobsData';
import { useVisitorTracking } from '../hooks/useVisitorTracking';

const Experience = () => {
  const { ref } = useVisitorTracking('experience', {
    threshold: 0.4,
    minDuration: 5
  });

  const handleCompanyLinkClick = (e) => {
    // Prevent the parent Link from being triggered when clicking the company link
    e.stopPropagation();
  };

  const handleTimelineItemClick = (jobSlug) => {
    // Store the current scroll position for this specific job
    const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    sessionStorage.setItem(`timeline-scroll-${jobSlug}`, currentScrollPosition.toString());
  };

  return (
    <section className="experience" ref={ref}>
      <h2>Professional Experience</h2>
      <div className="timeline">
        {jobsData.map((job, index) => (
          <div key={job.id} className="timeline-item">
            <div className="timeline-dot"></div>
            <Link
              to={`/job/${job.slug}`}
              className="timeline-content-link"
              onClick={() => handleTimelineItemClick(job.slug)}
            >
              <div className="timeline-content">
                <img
                  src={job.logo}
                  alt={job.logoAlt}
                  className="company-logo"
                />
                <h3 className="job-title">{job.title}</h3>
                <h4 className="company-name">{job.company}</h4>
                {job.companyLink && (
                  <p className="company-link">
                    <span
                      className="company-link-span"
                      onClick={handleCompanyLinkClick}
                      style={{ color: '#4B0082', textDecoration: 'underline', cursor: 'pointer' }}
                      role="link"
                      tabIndex={0}
                      onKeyPress={e => { if (e.key === 'Enter') window.open(job.companyLink, '_blank'); }}
                    >
                      {job.companyLink}
                    </span>
                  </p>
                )}
                <p className="job-duration">{job.duration}</p>
                <p className="job-description">{job.summary}</p>
                <div className="view-details">
                  <span>View Details →</span>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Experience;
