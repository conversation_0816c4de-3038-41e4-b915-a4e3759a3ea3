<!DOCTYPE html>
<html>
<head>
    <title>Render Deployment Test</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
        }
        .test-section { 
            margin: 25px 0; 
            padding: 20px; 
            border: 1px solid rgba(255,255,255,0.2); 
            border-radius: 10px; 
            background: rgba(255,255,255,0.05);
        }
        .success { color: #4ade80; font-weight: bold; }
        .error { color: #f87171; font-weight: bold; }
        .info { color: #60a5fa; }
        .warning { color: #fbbf24; }
        button { 
            margin: 8px; 
            padding: 12px 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-weight: 600;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .test-result { 
            margin: 15px 0; 
            padding: 15px; 
            border-left: 4px solid #ccc; 
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .test-result.success { border-left-color: #4ade80; }
        .test-result.error { border-left-color: #f87171; }
        .test-result.info { border-left-color: #60a5fa; }
        .test-result.warning { border-left-color: #fbbf24; }
        .url-test {
            background: rgba(255,255,255,0.05);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Render Deployment Test</h1>
        <h2>Testing Fixed Asset Paths & MIME Types</h2>
        
        <div class="test-section">
            <h3>🌐 Render Site Status</h3>
            <p>Testing if the Render deployment is working:</p>
            <div id="render-status"></div>
            <button onclick="testRenderStatus()">Test Render Site</button>
        </div>

        <div class="test-section">
            <h3>📁 Asset Path Test</h3>
            <p>Testing if assets are loading from correct paths:</p>
            <div id="asset-paths"></div>
            <button onclick="testAssetPaths()">Test Asset Paths</button>
        </div>

        <div class="test-section">
            <h3>🔧 MIME Type Test</h3>
            <p>Testing if MIME types are correct:</p>
            <div id="mime-types"></div>
            <button onclick="testMimeTypes()">Test MIME Types</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let testResults = [];

        function addResult(type, title, message, details = '') {
            const result = { 
                type, 
                title, 
                message, 
                details, 
                timestamp: new Date().toLocaleString('en-US', { timeZone: 'Africa/Tunis' })
            };
            testResults.push(result);
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>📊 Test Results</h3>' + 
                testResults.map(result => `
                    <div class="test-result ${result.type}">
                        <strong>[${result.timestamp}] ${result.title}</strong><br>
                        ${result.message}
                        ${result.details ? `<br><small>${result.details}</small>` : ''}
                    </div>
                `).join('');
        }

        async function testRenderStatus() {
            const statusDiv = document.getElementById('render-status');
            statusDiv.innerHTML = '<div class="info">Testing Render deployment...</div>';
            
            const testUrls = [
                'https://porfolio-pro.onrender.com/',
                'https://porfolio-pro.onrender.com/admin/login',
                'https://porfolio-pro.onrender.com/job/senior-fullstack-developer'
            ];
            
            let results = [];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    if (response.ok) {
                        results.push(`✅ ${url} - Status: ${response.status}`);
                    } else {
                        results.push(`❌ ${url} - Status: ${response.status}`);
                    }
                } catch (error) {
                    results.push(`❌ ${url} - Error: ${error.message}`);
                }
            }
            
            statusDiv.innerHTML = results.map(r => `<div class="url-test">${r}</div>`).join('');
            
            const successCount = results.filter(r => r.includes('✅')).length;
            if (successCount === testUrls.length) {
                addResult('success', '✅ Render Deployment', 'All URLs are accessible on Render');
            } else {
                addResult('warning', '⚠️ Render Deployment', `${successCount}/${testUrls.length} URLs accessible`);
            }
        }

        async function testAssetPaths() {
            const assetDiv = document.getElementById('asset-paths');
            assetDiv.innerHTML = '<div class="info">Testing asset paths...</div>';
            
            // Test expected asset URLs (these should work now)
            const expectedAssets = [
                'https://porfolio-pro.onrender.com/static/js/main.3d47c6a6.js',
                'https://porfolio-pro.onrender.com/static/css/main.877d552b.css',
                'https://porfolio-pro.onrender.com/logo.PNG',
                'https://porfolio-pro.onrender.com/manifest.json'
            ];
            
            let results = [];
            
            for (const url of expectedAssets) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    if (response.ok) {
                        results.push(`✅ ${url} - Status: ${response.status}`);
                    } else {
                        results.push(`❌ ${url} - Status: ${response.status}`);
                    }
                } catch (error) {
                    results.push(`❌ ${url} - Error: ${error.message}`);
                }
            }
            
            assetDiv.innerHTML = results.map(r => `<div class="url-test">${r}</div>`).join('');
            
            const successCount = results.filter(r => r.includes('✅')).length;
            if (successCount >= 3) {
                addResult('success', '✅ Asset Paths', 'Assets are loading from correct paths');
            } else {
                addResult('error', '❌ Asset Paths', 'Some assets are not loading correctly');
            }
        }

        async function testMimeTypes() {
            const mimeDiv = document.getElementById('mime-types');
            mimeDiv.innerHTML = '<div class="info">Testing MIME types...</div>';
            
            const assetTests = [
                { url: 'https://porfolio-pro.onrender.com/static/js/main.3d47c6a6.js', expectedType: 'application/javascript' },
                { url: 'https://porfolio-pro.onrender.com/static/css/main.877d552b.css', expectedType: 'text/css' },
                { url: 'https://porfolio-pro.onrender.com/logo.PNG', expectedType: 'image/png' }
            ];
            
            let results = [];
            
            for (const test of assetTests) {
                try {
                    const response = await fetch(test.url, { method: 'HEAD' });
                    const contentType = response.headers.get('content-type');
                    
                    if (response.ok && contentType && contentType.includes(test.expectedType.split('/')[0])) {
                        results.push(`✅ ${test.url} - MIME: ${contentType}`);
                    } else {
                        results.push(`❌ ${test.url} - MIME: ${contentType || 'Unknown'} (Expected: ${test.expectedType})`);
                    }
                } catch (error) {
                    results.push(`❌ ${test.url} - Error: ${error.message}`);
                }
            }
            
            mimeDiv.innerHTML = results.map(r => `<div class="url-test">${r}</div>`).join('');
            
            const successCount = results.filter(r => r.includes('✅')).length;
            if (successCount === assetTests.length) {
                addResult('success', '✅ MIME Types', 'All assets have correct MIME types');
            } else {
                addResult('error', '❌ MIME Types', 'Some assets have incorrect MIME types');
            }
        }

        // Auto-run tests when page loads
        window.onload = () => {
            setTimeout(() => {
                addResult('info', '🔄 Auto-Test', 'Starting Render deployment tests...');
                setTimeout(testRenderStatus, 1000);
                setTimeout(testAssetPaths, 3000);
                setTimeout(testMimeTypes, 5000);
            }, 1000);
        };
    </script>
</body>
</html>
