<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Geolocation Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .loading { color: #ff9800; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .flag-display {
            font-size: 24px;
            margin: 10px 0;
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", sans-serif;
        }
        .visitor-card {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .country-flag {
            font-size: 2rem;
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", sans-serif;
        }
    </style>
</head>
<body>
    <h1>🧪 Complete Geolocation Flow Test</h1>
    <p>This tests the entire geolocation flow from backend to frontend display.</p>

    <div class="test-section">
        <h2>1. Backend Geolocation API Test</h2>
        <button onclick="testBackendGeolocation()">Test Backend with Real IPs</button>
        <div id="backend-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Flag Generation Test</h2>
        <button onclick="testFlagGeneration()">Test Flag Generation</button>
        <div id="flag-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Frontend Display Test</h2>
        <button onclick="testFrontendDisplay()">Test Frontend Flag Display</button>
        <div id="frontend-result"></div>
    </div>

    <script>
        const API_URL = 'https://porfolio-pro-backend.onrender.com';

        async function getAuthToken() {
            let token = localStorage.getItem('token');
            if (!token) {
                try {
                    const response = await fetch(`${API_URL}/api/admin/login`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'Adminboss'
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        token = data.token;
                        localStorage.setItem('token', token);
                    }
                } catch (error) {
                    console.error('Login error:', error);
                }
            }
            return token;
        }

        async function testBackendGeolocation() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<div class="loading">Testing backend geolocation with real visitor IPs...</div>';
            
            try {
                const token = await getAuthToken();
                
                // Use real IPs from your visitor logs
                const testIPs = [
                    '*************',     // Tunisia
                    '**************',    // Tunisia
                    '*************',     // Tunisia
                    '************',      // Tunisia
                    '***************',   // France/Europe
                    '*******',           // US DNS
                    '127.0.0.1'          // Local
                ];
                
                const response = await fetch(`${API_URL}/api/admin/geolocation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ ips: testIPs })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                let resultHTML = `<div class="success">✅ Backend geolocation working!</div>`;
                let tunisiaCount = 0;
                let flagCount = 0;
                
                Object.entries(data.data).forEach(([ip, geo]) => {
                    const status = geo.error ? '❌' : '✅';
                    const flag = geo.flag || '🌍';
                    const location = geo.error ? geo.errorMessage : `${geo.country}, ${geo.city}`;
                    
                    if (geo.country === 'Tunisia') tunisiaCount++;
                    if (flag && flag !== '🌍') flagCount++;
                    
                    resultHTML += `
                        <div class="visitor-card">
                            <span class="country-flag">${flag}</span>
                            <div>
                                <div><strong>${ip}</strong></div>
                                <div>${status} ${location}</div>
                            </div>
                        </div>
                    `;
                });
                
                resultHTML += `
                    <div class="info">📊 Results Summary:</div>
                    <div class="info">🇹🇳 Tunisia IPs detected: ${tunisiaCount}</div>
                    <div class="info">🏁 Flags generated: ${flagCount}</div>
                    <div class="info">📈 Total IPs processed: ${Object.keys(data.data).length}</div>
                `;
                
                if (tunisiaCount > 0) {
                    resultHTML += '<div class="success">🎉 Tunisia detection working!</div>';
                }
                
                if (flagCount > 0) {
                    resultHTML += '<div class="success">🏁 Flag generation working!</div>';
                }
                
                resultDiv.innerHTML = resultHTML;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Backend test failed!</div>
                    <div class="error">Error: ${error.message}</div>
                    <div class="warning">Make sure the backend is deployed and running.</div>
                `;
            }
        }

        function testFlagGeneration() {
            const resultDiv = document.getElementById('flag-result');
            
            const countries = [
                { code: 'TN', name: 'Tunisia' },
                { code: 'FR', name: 'France' },
                { code: 'US', name: 'United States' },
                { code: 'DZ', name: 'Algeria' },
                { code: 'MA', name: 'Morocco' },
                { code: 'DE', name: 'Germany' },
                { code: 'GB', name: 'United Kingdom' },
                { code: 'IT', name: 'Italy' },
                { code: 'ES', name: 'Spain' },
                { code: 'CA', name: 'Canada' }
            ];
            
            let resultHTML = '<div class="success">✅ Local flag generation (no API needed):</div>';
            
            countries.forEach(country => {
                const flag = generateFlag(country.code);
                resultHTML += `
                    <div class="flag-display">
                        ${country.code} → ${flag} ${country.name}
                    </div>
                `;
            });
            
            resultHTML += '<div class="info">🚀 All flags generated locally using Unicode!</div>';
            resultDiv.innerHTML = resultHTML;
        }

        function generateFlag(countryCode) {
            if (!countryCode || countryCode === 'UN' || countryCode === 'LO') {
                return '🌍';
            }
            
            const codePoints = countryCode
                .toUpperCase()
                .split('')
                .map(char => 127397 + char.charCodeAt());
            
            return String.fromCodePoint(...codePoints);
        }

        function testFrontendDisplay() {
            const resultDiv = document.getElementById('frontend-result');
            
            // Simulate visitor data with flags
            const mockVisitors = [
                { ip: '*************', geo: { country: 'Tunisia', city: 'Tunis', flag: '🇹🇳' } },
                { ip: '*************', geo: { country: 'Tunisia', city: 'Tunis', flag: '🇹🇳' } },
                { ip: '***************', geo: { country: 'France', city: 'Paris', flag: '🇫🇷' } },
                { ip: '*******', geo: { country: 'United States', city: 'Mountain View', flag: '🇺🇸' } },
                { ip: '127.0.0.1', geo: { country: 'Local', city: 'Localhost', flag: '🏠' } }
            ];
            
            let resultHTML = '<div class="success">✅ Frontend display simulation:</div>';
            
            mockVisitors.forEach(visitor => {
                resultHTML += `
                    <div class="visitor-card">
                        <span class="country-flag">${visitor.geo.flag}</span>
                        <div>
                            <div><strong>${visitor.ip}</strong></div>
                            <div>${visitor.geo.country}, ${visitor.geo.city}</div>
                        </div>
                    </div>
                `;
            });
            
            resultHTML += '<div class="info">🎨 This is how flags should appear in your All Visitors page!</div>';
            resultDiv.innerHTML = resultHTML;
        }

        // Auto-run flag generation test on page load
        window.onload = () => {
            testFlagGeneration();
        };
    </script>
</body>
</html>
