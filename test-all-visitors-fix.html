<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Visitors Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .loading { color: #ff9800; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 Test All Visitors Fix</h1>
    <p>This page tests the fixes applied to the All Visitors page to prevent infinite loading.</p>

    <div class="test-section">
        <h2>1. Backend Connection Test</h2>
        <button onclick="testBackendConnection()">Test Backend</button>
        <div id="backend-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Dashboard API Test</h2>
        <button onclick="testDashboardAPI()">Test Dashboard API</button>
        <div id="dashboard-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Geolocation API Test</h2>
        <button onclick="testGeolocationAPI()">Test Geolocation API</button>
        <div id="geolocation-result"></div>
    </div>

    <div class="test-section">
        <h2>4. All Visitors Page Test</h2>
        <button onclick="openAllVisitorsPage()">Open All Visitors Page</button>
        <div id="visitors-result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:5000';
        let authToken = null;

        // Get auth token from localStorage or login
        async function getAuthToken() {
            authToken = localStorage.getItem('token');
            if (!authToken) {
                console.log('No token found, attempting login...');
                try {
                    const response = await fetch(`${API_URL}/api/admin/login`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'Adminboss'
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        authToken = data.token;
                        localStorage.setItem('token', authToken);
                        console.log('Login successful');
                    } else {
                        throw new Error('Login failed');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    throw error;
                }
            }
            return authToken;
        }

        async function testBackendConnection() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<div class="loading">Testing backend connection...</div>';
            
            try {
                const response = await fetch(`${API_URL}/api/ping`);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Backend is running!</div>
                    <div class="info">Status: ${data.status}</div>
                    <div class="info">Message: ${data.message}</div>
                    <div class="info">Timestamp: ${data.timestamp}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Backend connection failed!</div>
                    <div class="error">Error: ${error.message}</div>
                    <div class="warning">Make sure the backend server is running on port 5000</div>
                `;
            }
        }

        async function testDashboardAPI() {
            const resultDiv = document.getElementById('dashboard-result');
            resultDiv.innerHTML = '<div class="loading">Testing dashboard API...</div>';
            
            try {
                const token = await getAuthToken();
                
                const response = await fetch(`${API_URL}/api/admin/dashboard`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Dashboard API working!</div>
                    <div class="info">Total visits: ${data.totalVisits}</div>
                    <div class="info">Unique visitors: ${data.uniqueVisitors}</div>
                    <div class="info">Recent visits: ${data.visits ? data.visits.length : 0}</div>
                    <div class="info">Sample IPs: ${data.visits ? data.visits.slice(0, 3).map(v => v.ip).join(', ') : 'None'}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Dashboard API failed!</div>
                    <div class="error">Error: ${error.message}</div>
                `;
            }
        }

        async function testGeolocationAPI() {
            const resultDiv = document.getElementById('geolocation-result');
            resultDiv.innerHTML = '<div class="loading">Testing geolocation API...</div>';
            
            try {
                const token = await getAuthToken();
                const testIPs = ['*******', '127.0.0.1', '*******'];
                
                const response = await fetch(`${API_URL}/api/admin/geolocation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ ips: testIPs })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                let resultHTML = `
                    <div class="success">✅ Geolocation API working!</div>
                    <div class="info">Processed ${data.processedCount} IPs</div>
                    <div class="info">Results:</div>
                    <pre>
                `;
                
                Object.entries(data.data).forEach(([ip, geo]) => {
                    const status = geo.error ? '❌' : '✅';
                    const location = geo.error ? geo.errorMessage : `${geo.country}, ${geo.city} ${geo.flag}`;
                    resultHTML += `${status} ${ip} → ${location}\n`;
                });
                
                resultHTML += '</pre>';
                resultDiv.innerHTML = resultHTML;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Geolocation API failed!</div>
                    <div class="error">Error: ${error.message}</div>
                `;
            }
        }

        function openAllVisitorsPage() {
            const resultDiv = document.getElementById('visitors-result');
            resultDiv.innerHTML = `
                <div class="info">Opening All Visitors page...</div>
                <div class="warning">Check if the page loads within 30 seconds</div>
                <div class="info">If it shows "Loading location data..." that's normal - it should still show the visitor list</div>
            `;
            
            // Open in new tab
            window.open('http://localhost:3000/admin/all-visitors', '_blank');
        }

        // Auto-test backend connection on page load
        window.onload = () => {
            testBackendConnection();
        };
    </script>
</body>
</html>
