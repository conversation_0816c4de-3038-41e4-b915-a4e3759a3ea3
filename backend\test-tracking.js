const http = require('http');

function makeRequest(data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/track/visit',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: responseData
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.write(postData);
    req.end();
  });
}

async function testTracking() {
  console.log('🚀 Testing tracking endpoint...');
  
  const testVisits = [
    { section: 'hero', duration: 15, sessionId: 'test-session-1' },
    { section: 'about', duration: 25, sessionId: 'test-session-2' },
    { section: 'portfolio', duration: 45, sessionId: 'test-session-3' },
    { section: 'contact', duration: 12, sessionId: 'test-session-4' }
  ];
  
  for (const visit of testVisits) {
    try {
      const response = await makeRequest(visit);
      console.log(`✅ ${visit.section}: Status ${response.statusCode} - ${response.data}`);
    } catch (error) {
      console.log(`❌ ${visit.section}: Error - ${error.message}`);
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('🎉 Tracking test complete!');
}

testTracking();
