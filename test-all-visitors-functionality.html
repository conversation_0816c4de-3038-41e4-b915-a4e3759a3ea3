<!DOCTYPE html>
<html>
<head>
    <title>All Visitors Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>All Visitors Details - Comprehensive Test</h1>
    
    <div class="test-section">
        <h2>1. Backend Connection Test</h2>
        <button onclick="testBackendConnection()">Test Backend</button>
        <div id="backend-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Admin Login Test</h2>
        <button onclick="testAdminLogin()">Test Admin Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Visitor Data Fetch Test</h2>
        <button onclick="testVisitorData()">Test Visitor Data</button>
        <div id="visitor-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Timezone Display Test</h2>
        <button onclick="testTimezoneDisplay()">Test Tunisia Timezone</button>
        <div id="timezone-result"></div>
    </div>

    <div class="test-section">
        <h2>5. Social Media Meta Tags Test</h2>
        <button onclick="testMetaTags()">Test Meta Tags</button>
        <div id="meta-result"></div>
    </div>

    <div id="results"></div>

    <script>
        const API_URL = 'https://porfolio-pro-backend.onrender.com';
        let authToken = null;

        async function testBackendConnection() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<div class="info">Testing backend connection...</div>';
            
            try {
                const response = await fetch(`${API_URL}/api/ping`);
                const data = await response.json();
                resultDiv.innerHTML = `<div class="success">✅ Backend is running! Response: ${JSON.stringify(data)}</div>`;
                return true;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Backend connection failed: ${error.message}</div>`;
                return false;
            }
        }

        async function testAdminLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<div class="info">Testing admin login...</div>';
            
            try {
                const response = await fetch(`${API_URL}/api/admin/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        email: '<EMAIL>', 
                        password: 'Adminboss' 
                    })
                });
                
                const data = await response.json();
                if (data.token) {
                    authToken = data.token;
                    resultDiv.innerHTML = '<div class="success">✅ Admin login successful!</div>';
                    return true;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Login failed: ${data.message}</div>`;
                    return false;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Login error: ${error.message}</div>`;
                return false;
            }
        }

        async function testVisitorData() {
            const resultDiv = document.getElementById('visitor-result');
            resultDiv.innerHTML = '<div class="info">Testing visitor data fetch...</div>';
            
            if (!authToken) {
                resultDiv.innerHTML = '<div class="error">❌ Please login first</div>';
                return false;
            }
            
            try {
                const response = await fetch(`${API_URL}/api/admin/dashboard`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                const data = await response.json();
                const visitCount = data.visits ? data.visits.length : 0;
                const uniqueVisitors = data.uniqueVisitors || 0;
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Visitor data fetched successfully!</div>
                    <div class="info">Total visits: ${visitCount}</div>
                    <div class="info">Unique visitors: ${uniqueVisitors}</div>
                    <div class="info">Recent visits: ${data.visits ? data.visits.slice(0, 5).map(v => v.ip).join(', ') : 'None'}</div>
                `;
                
                // Test if we're getting more than just 20 visits (the old limit)
                if (visitCount > 20) {
                    resultDiv.innerHTML += '<div class="success">✅ Visitor limit fix working - showing more than 20 visits!</div>';
                } else if (visitCount > 0) {
                    resultDiv.innerHTML += '<div class="info">ℹ️ Currently have ' + visitCount + ' visits (limit fix applied but may need more data)</div>';
                }
                
                return true;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Visitor data fetch failed: ${error.message}</div>`;
                return false;
            }
        }

        function testTimezoneDisplay() {
            const resultDiv = document.getElementById('timezone-result');
            resultDiv.innerHTML = '<div class="info">Testing Tunisia timezone display...</div>';
            
            try {
                const testDate = new Date();
                const tunisiaTime = testDate.toLocaleString('en-US', {
                    timeZone: 'Africa/Tunis',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const localTime = testDate.toLocaleString();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Timezone formatting working!</div>
                    <div class="info">Local time: ${localTime}</div>
                    <div class="info">Tunisia time: ${tunisiaTime}</div>
                `;
                return true;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Timezone test failed: ${error.message}</div>`;
                return false;
            }
        }

        function testMetaTags() {
            const resultDiv = document.getElementById('meta-result');
            resultDiv.innerHTML = '<div class="info">Testing social media meta tags...</div>';
            
            try {
                const ogImage = document.querySelector('meta[property="og:image"]');
                const twitterImage = document.querySelector('meta[name="twitter:image"]');
                const ogUrl = document.querySelector('meta[property="og:url"]');
                
                let results = [];
                
                if (ogImage && ogImage.content.includes('aminos555.github.io/Porfolio-Pro/logo.PNG')) {
                    results.push('<div class="success">✅ Open Graph image URL updated correctly</div>');
                } else {
                    results.push('<div class="error">❌ Open Graph image URL not updated</div>');
                }
                
                if (twitterImage && twitterImage.content.includes('aminos555.github.io/Porfolio-Pro/logo.PNG')) {
                    results.push('<div class="success">✅ Twitter image URL updated correctly</div>');
                } else {
                    results.push('<div class="error">❌ Twitter image URL not updated</div>');
                }
                
                if (ogUrl && ogUrl.content.includes('aminos555.github.io/Porfolio-Pro/')) {
                    results.push('<div class="success">✅ Open Graph URL updated correctly</div>');
                } else {
                    results.push('<div class="error">❌ Open Graph URL not updated</div>');
                }
                
                resultDiv.innerHTML = results.join('');
                return true;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Meta tags test failed: ${error.message}</div>`;
                return false;
            }
        }

        async function runAllTests() {
            document.getElementById('results').innerHTML = '<h2>Running All Tests...</h2>';
            
            const backendOk = await testBackendConnection();
            if (!backendOk) return;
            
            const loginOk = await testAdminLogin();
            if (!loginOk) return;
            
            await testVisitorData();
            testTimezoneDisplay();
            testMetaTags();
            
            document.getElementById('results').innerHTML += '<h2>All tests completed!</h2>';
        }

        // Auto-run tests on page load
        window.onload = () => {
            setTimeout(runAllTests, 1000);
        };
    </script>
</body>
</html>
