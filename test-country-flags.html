<!DOCTYPE html>
<html>
<head>
    <title>Country Flags Test</title>
    <style>
        body { 
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", sans-serif;
            margin: 20px; 
            background: #1a1a2e;
            color: white;
        }
        .flag-test {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        .country-flag {
            font-size: 2rem;
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            margin-right: 10px;
        }
        .flag-row {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 5px;
        }
        @media (max-width: 768px) {
            .country-flag {
                font-size: 1.8rem;
            }
        }
        @media (max-width: 480px) {
            .country-flag {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <h1>🏳️ Country Flags Display Test</h1>
    
    <div class="flag-test">
        <h2>Flag Rendering Test</h2>
        <p>Testing country flags with the same CSS as AllVisitorsDetails component:</p>
        
        <div class="flag-row">
            <span class="country-flag">🇹🇳</span> Tunisia (TN)
        </div>
        <div class="flag-row">
            <span class="country-flag">🇫🇷</span> France (FR)
        </div>
        <div class="flag-row">
            <span class="country-flag">🇺🇸</span> United States (US)
        </div>
        <div class="flag-row">
            <span class="country-flag">🇩🇪</span> Germany (DE)
        </div>
        <div class="flag-row">
            <span class="country-flag">🇬🇧</span> United Kingdom (GB)
        </div>
        <div class="flag-row">
            <span class="country-flag">🇨🇦</span> Canada (CA)
        </div>
        <div class="flag-row">
            <span class="country-flag">🌍</span> Unknown/Default
        </div>
        <div class="flag-row">
            <span class="country-flag">🏠</span> Local/Localhost
        </div>
    </div>

    <div class="flag-test">
        <h2>Flag Generation Test</h2>
        <p>Testing the flag generation function from geolocation.js:</p>
        <div id="flag-generation-test"></div>
        <button onclick="testFlagGeneration()">Test Flag Generation</button>
    </div>

    <div class="flag-test">
        <h2>Mobile Responsiveness Test</h2>
        <p>Resize your browser window to test mobile flag sizes:</p>
        <div style="border: 1px solid #666; padding: 10px; margin: 10px 0;">
            <span class="country-flag">🇹🇳</span> Desktop Size (2rem)
        </div>
        <div style="border: 1px solid #666; padding: 10px; margin: 10px 0;">
            <span class="country-flag" style="font-size: 1.8rem;">🇹🇳</span> Tablet Size (1.8rem)
        </div>
        <div style="border: 1px solid #666; padding: 10px; margin: 10px 0;">
            <span class="country-flag" style="font-size: 1.5rem;">🇹🇳</span> Mobile Size (1.5rem)
        </div>
    </div>

    <script>
        // Replicate the flag generation function from geolocation.js
        function getCountryFlag(countryCode) {
            if (!countryCode || countryCode.length !== 2) return '🌍';
            
            const codePoints = countryCode
                .toUpperCase()
                .split('')
                .map(char => 127397 + char.charCodeAt());
            
            return String.fromCodePoint(...codePoints);
        }

        function testFlagGeneration() {
            const testCodes = ['TN', 'FR', 'US', 'DE', 'GB', 'CA', 'JP', 'AU', 'BR', 'IN'];
            const resultDiv = document.getElementById('flag-generation-test');
            
            let html = '<h3>Generated Flags:</h3>';
            testCodes.forEach(code => {
                const flag = getCountryFlag(code);
                html += `
                    <div class="flag-row">
                        <span class="country-flag">${flag}</span> ${code} → ${flag}
                    </div>
                `;
            });
            
            // Test edge cases
            html += '<h4>Edge Cases:</h4>';
            html += `<div class="flag-row"><span class="country-flag">${getCountryFlag('')}</span> Empty string → ${getCountryFlag('')}</div>`;
            html += `<div class="flag-row"><span class="country-flag">${getCountryFlag('X')}</span> Single char → ${getCountryFlag('X')}</div>`;
            html += `<div class="flag-row"><span class="country-flag">${getCountryFlag('XXX')}</span> Three chars → ${getCountryFlag('XXX')}</div>`;
            html += `<div class="flag-row"><span class="country-flag">${getCountryFlag(null)}</span> Null → ${getCountryFlag(null)}</div>`;
            
            resultDiv.innerHTML = html;
        }

        // Test on page load
        window.onload = () => {
            testFlagGeneration();
        };
    </script>
</body>
</html>
