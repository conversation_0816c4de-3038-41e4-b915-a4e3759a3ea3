require('dotenv').config();
const { batchGetCountriesFromIPs } = require('./utils/geolocation');

async function analyzeUnknownIPs() {
  console.log('🔍 ANALYZING UNKNOWN IP PATTERNS');
  console.log('================================\n');
  
  // Sample of IPs that are showing as Unknown (based on your feedback)
  const unknownIPs = [
    '**************',    // Your example - likely Tunisia
    '**************',    // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '*************',     // Similar pattern
    '************',      // Might be Tunisia
    '************',      // Might be Tunisia
    '************',      // Might be Tunisia
    '************',      // Might be Tunisia
    '************',      // Might be Tunisia
    '************',      // North Africa range
    '************',      // North Africa range
    '************',      // North Africa range
    '*************',     // Africa range
    '*************',     // Africa range
    '*************',     // Africa range
    '*************',     // Algeria/Tunisia range
    '*************',     // Algeria/Tunisia range
    '*************',     // Algeria/Tunisia range
    '*************',     // Morocco/North Africa
    '*************',     // Morocco/North Africa
    '*************'      // Morocco/North Africa
  ];
  
  console.log('🎯 Testing potentially missing IP ranges:');
  unknownIPs.forEach(ip => console.log(`   - ${ip}`));
  console.log('\n🚀 Running geolocation analysis...\n');
  
  try {
    const results = await batchGetCountriesFromIPs(unknownIPs);
    
    console.log('📊 ANALYSIS RESULTS:');
    console.log('===================\n');
    
    const patterns = {};
    let unknownCount = 0;
    let identifiedCount = 0;
    
    Object.entries(results).forEach(([ip, data]) => {
      const status = data.error ? '❌' : '✅';
      const location = data.error 
        ? `UNKNOWN (${data.errorMessage})` 
        : `${data.country}, ${data.city} ${data.flag}`;
      
      console.log(`${status} ${ip.padEnd(17)} → ${location}`);
      
      if (data.error || data.country === 'Unknown') {
        unknownCount++;
        
        // Extract IP pattern for analysis
        const pattern = ip.split('.').slice(0, 2).join('.') + '.x.x';
        if (!patterns[pattern]) {
          patterns[pattern] = [];
        }
        patterns[pattern].push(ip);
      } else {
        identifiedCount++;
      }
    });
    
    console.log('\n📈 PATTERN ANALYSIS:');
    console.log('====================');
    console.log(`✅ Successfully identified: ${identifiedCount}`);
    console.log(`❌ Still unknown: ${unknownCount}`);
    
    if (Object.keys(patterns).length > 0) {
      console.log('\n🔍 MISSING IP PATTERNS TO ADD:');
      console.log('==============================');
      
      Object.entries(patterns).forEach(([pattern, ips]) => {
        console.log(`\n📍 Pattern: ${pattern}`);
        console.log(`   IPs: ${ips.join(', ')}`);
        console.log(`   Likely Country: ${guessCountryFromPattern(pattern)}`);
        console.log(`   Suggested Code: ${generateHeuristicCode(pattern)}`);
      });
    }
    
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('===================');
    console.log('1. Add the missing IP patterns to backend/utils/geolocation.js');
    console.log('2. Update both backend and frontend heuristic functions');
    console.log('3. Test with real visitor IPs from your database');
    console.log('4. Deploy and verify in production');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
  }
}

function guessCountryFromPattern(pattern) {
  if (pattern.startsWith('102.')) return 'Tunisia (likely)';
  if (pattern.startsWith('41.')) return 'Tunisia/Algeria (likely)';
  if (pattern.startsWith('197.')) return 'Tunisia/North Africa (likely)';
  if (pattern.startsWith('196.')) return 'Africa (various countries)';
  if (pattern.startsWith('105.')) return 'Algeria/Tunisia (likely)';
  if (pattern.startsWith('160.')) return 'Morocco/North Africa (likely)';
  return 'Unknown region';
}

function generateHeuristicCode(pattern) {
  const prefix = pattern.replace('.x.x', '.');
  
  if (pattern.startsWith('102.')) {
    return `if (ip.startsWith('${prefix}')) { return getTunisiaData(); }`;
  }
  if (pattern.startsWith('41.')) {
    return `if (ip.startsWith('${prefix}')) { return getTunisiaData(); }`;
  }
  if (pattern.startsWith('197.')) {
    return `if (ip.startsWith('${prefix}')) { return getTunisiaData(); }`;
  }
  if (pattern.startsWith('196.')) {
    return `if (ip.startsWith('${prefix}')) { return getAfricaData(); }`;
  }
  if (pattern.startsWith('105.')) {
    return `if (ip.startsWith('${prefix}')) { return getAlgeriaData(); }`;
  }
  if (pattern.startsWith('160.')) {
    return `if (ip.startsWith('${prefix}')) { return getMoroccoData(); }`;
  }
  
  return `// Add pattern: ${pattern}`;
}

analyzeUnknownIPs();
