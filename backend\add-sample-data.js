require('dotenv').config();
const mongoose = require('mongoose');

// Define the Visit schema
const visitSchema = new mongoose.Schema({
  ip: { type: String, required: true },
  section: { type: String, required: true },
  duration: { type: Number, default: 0 },
  timestamp: { type: Date, default: Date.now },
  sessionId: String,
  pageUrl: String,
  userAgent: String,
  referrer: String,
  jobTitle: String,
  jobSlug: String,
  projectTitle: String,
  interactionType: String,
  cardTitle: String,
  skillCategory: String
});

const Visit = mongoose.model('Visit', visitSchema);

async function addSampleData() {
  console.log('📊 Adding Sample Visitor Data for Chart Testing');
  console.log('===============================================\n');

  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB\n');

    // Generate sample data for the last 7 days
    const sampleData = [];
    const sections = ['Home', 'About', 'Portfolio', 'Skills', 'Contact', 'Jobs'];
    const sampleIPs = [
      '*************', '*********', '***********', '************',
      '*************', '**********', '************', '*************'
    ];

    for (let day = 0; day < 7; day++) {
      const date = new Date();
      date.setDate(date.getDate() - day);
      date.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

      // Generate 5-15 visits per day
      const visitsPerDay = Math.floor(Math.random() * 10) + 5;
      
      for (let visit = 0; visit < visitsPerDay; visit++) {
        const visitTime = new Date(date);
        visitTime.setMinutes(visitTime.getMinutes() + (visit * 30) + Math.floor(Math.random() * 30));

        sampleData.push({
          ip: sampleIPs[Math.floor(Math.random() * sampleIPs.length)],
          section: sections[Math.floor(Math.random() * sections.length)],
          duration: Math.floor(Math.random() * 300) + 10, // 10-310 seconds
          timestamp: visitTime,
          sessionId: `session_${day}_${visit}`,
          pageUrl: `http://localhost:3000/${sections[Math.floor(Math.random() * sections.length)].toLowerCase()}`,
          userAgent: 'Mozilla/5.0 (Test Browser)',
          referrer: Math.random() > 0.5 ? 'https://google.com' : 'direct'
        });
      }
    }

    console.log(`📝 Generated ${sampleData.length} sample visits`);

    // Insert sample data
    await Visit.insertMany(sampleData);
    console.log('✅ Sample data inserted successfully!');

    // Verify the data
    const totalVisits = await Visit.countDocuments();
    const uniqueIPs = await Visit.distinct('ip');
    
    console.log('\n📊 Database Summary:');
    console.log(`Total visits: ${totalVisits}`);
    console.log(`Unique IPs: ${uniqueIPs.length}`);
    console.log(`Sample IPs: ${uniqueIPs.slice(0, 5).join(', ')}${uniqueIPs.length > 5 ? '...' : ''}`);

    console.log('\n🎉 Sample data ready! You can now test the charts in your admin dashboard.');

  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

addSampleData();
