const http = require('http');

function makeRequest(path, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : null;
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (postData) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: responseData,
          headers: res.headers
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function testAdmin() {
  console.log('🚀 Testing admin functionality...');
  
  try {
    // Test 1: Admin login
    console.log('\n📍 Test 1: Admin Login');
    const loginResponse = await makeRequest('/api/admin/login', 'POST', {
      email: '<EMAIL>',
      password: 'Adminboss'
    });
    
    if (loginResponse.statusCode === 200) {
      const loginData = JSON.parse(loginResponse.data);
      console.log('✅ Admin login successful');
      
      const token = loginData.token;
      
      // Test 2: Dashboard with token
      console.log('\n📍 Test 2: Dashboard Access');
      const dashboardResponse = await makeRequest('/api/admin/dashboard', 'GET', null, {
        'Authorization': `Bearer ${token}`
      });
      
      if (dashboardResponse.statusCode === 200) {
        const dashboardData = JSON.parse(dashboardResponse.data);
        console.log('✅ Dashboard access successful');
        console.log(`📊 Total visits: ${dashboardData.totalVisits}`);
        console.log(`👥 Unique visitors: ${dashboardData.visits.length > 0 ? new Set(dashboardData.visits.map(v => v.ip)).size : 0}`);
        
        // Show sample IPs for geolocation testing
        if (dashboardData.visits.length > 0) {
          const uniqueIPs = [...new Set(dashboardData.visits.map(v => v.ip))];
          console.log(`🌍 Sample IPs: ${uniqueIPs.slice(0, 3).join(', ')}`);
          
          // Test geolocation for first IP
          if (uniqueIPs.length > 0) {
            console.log('\n📍 Test 3: Geolocation Test');
            await testGeolocation(uniqueIPs[0]);
          }
        }
      } else {
        console.log(`❌ Dashboard access failed: ${dashboardResponse.statusCode}`);
      }
    } else {
      console.log(`❌ Admin login failed: ${loginResponse.statusCode}`);
    }
    
  } catch (error) {
    console.error('❌ Admin test error:', error);
  }
}

async function testGeolocation(ip) {
  try {
    const https = require('https');
    
    return new Promise((resolve, reject) => {
      const req = https.request(`https://ipapi.co/${ip}/json/`, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const geoData = JSON.parse(data);
            if (geoData.error) {
              console.log(`❌ Geolocation error for ${ip}: ${geoData.reason}`);
            } else {
              console.log(`✅ Geolocation for ${ip}: ${geoData.country_name}, ${geoData.city}`);
            }
            resolve();
          } catch (error) {
            console.log(`❌ Geolocation parse error: ${error.message}`);
            resolve();
          }
        });
      });
      
      req.on('error', (error) => {
        console.log(`❌ Geolocation request error: ${error.message}`);
        resolve();
      });
      
      req.end();
    });
  } catch (error) {
    console.log(`❌ Geolocation test error: ${error.message}`);
  }
}

testAdmin();
