<!DOCTYPE html>
<html>
<head>
    <title>Deployment Fix Verification</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
        }
        .test-section { 
            margin: 25px 0; 
            padding: 20px; 
            border: 1px solid rgba(255,255,255,0.2); 
            border-radius: 10px; 
            background: rgba(255,255,255,0.05);
        }
        .success { color: #4ade80; font-weight: bold; }
        .error { color: #f87171; font-weight: bold; }
        .info { color: #60a5fa; }
        .warning { color: #fbbf24; }
        button { 
            margin: 8px; 
            padding: 12px 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-weight: 600;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .test-result { 
            margin: 15px 0; 
            padding: 15px; 
            border-left: 4px solid #ccc; 
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .test-result.success { border-left-color: #4ade80; }
        .test-result.error { border-left-color: #f87171; }
        .test-result.info { border-left-color: #60a5fa; }
        .test-result.warning { border-left-color: #fbbf24; }
        .url-test {
            background: rgba(255,255,255,0.05);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Deployment Fix Verification</h1>
        <h2>Testing GitHub Pages Deployment & MIME Type Fixes</h2>
        
        <div class="test-section">
            <h3>🌐 Deployment Status Check</h3>
            <p>Testing if the GitHub Pages deployment is working correctly:</p>
            <div id="deployment-status"></div>
            <button onclick="testDeploymentStatus()">Test Deployment</button>
        </div>

        <div class="test-section">
            <h3>📁 Asset Loading Test</h3>
            <p>Testing if CSS and JS assets are loading with correct MIME types:</p>
            <div id="asset-loading"></div>
            <button onclick="testAssetLoading()">Test Assets</button>
        </div>

        <div class="test-section">
            <h3>🔄 SPA Routing Test</h3>
            <p>Testing if Single Page Application routing works correctly:</p>
            <div id="routing-test"></div>
            <button onclick="testSPARouting()">Test Routing</button>
        </div>

        <div class="test-section">
            <h3>🏷️ Meta Tags Verification</h3>
            <p>Verifying social media meta tags are correctly updated:</p>
            <div id="meta-verification"></div>
            <button onclick="verifyMetaTags()">Verify Meta Tags</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let testResults = [];

        function addResult(type, title, message, details = '') {
            const result = { 
                type, 
                title, 
                message, 
                details, 
                timestamp: new Date().toLocaleString('en-US', { timeZone: 'Africa/Tunis' })
            };
            testResults.push(result);
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>📊 Test Results</h3>' + 
                testResults.map(result => `
                    <div class="test-result ${result.type}">
                        <strong>[${result.timestamp}] ${result.title}</strong><br>
                        ${result.message}
                        ${result.details ? `<br><small>${result.details}</small>` : ''}
                    </div>
                `).join('');
        }

        async function testDeploymentStatus() {
            const statusDiv = document.getElementById('deployment-status');
            statusDiv.innerHTML = '<div class="info">Testing deployment status...</div>';
            
            const testUrls = [
                'https://aminos555.github.io/Porfolio-Pro/',
                'https://aminos555.github.io/Porfolio-Pro/admin/login',
                'https://aminos555.github.io/Porfolio-Pro/job/senior-fullstack-developer'
            ];
            
            let results = [];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    if (response.ok) {
                        results.push(`✅ ${url} - Status: ${response.status}`);
                    } else {
                        results.push(`❌ ${url} - Status: ${response.status}`);
                    }
                } catch (error) {
                    results.push(`❌ ${url} - Error: ${error.message}`);
                }
            }
            
            statusDiv.innerHTML = results.map(r => `<div class="url-test">${r}</div>`).join('');
            
            const successCount = results.filter(r => r.includes('✅')).length;
            if (successCount === testUrls.length) {
                addResult('success', '✅ Deployment Status', 'All URLs are accessible');
            } else {
                addResult('warning', '⚠️ Deployment Status', `${successCount}/${testUrls.length} URLs accessible`);
            }
        }

        async function testAssetLoading() {
            const assetDiv = document.getElementById('asset-loading');
            assetDiv.innerHTML = '<div class="info">Testing asset loading...</div>';
            
            // Test if we can detect the current page's assets
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            const jsScripts = document.querySelectorAll('script[src]');
            
            let results = [];
            results.push(`📄 Found ${cssLinks.length} CSS files`);
            results.push(`📄 Found ${jsScripts.length} JS files`);
            
            // Test a sample asset URL structure
            const expectedAssetPath = 'https://aminos555.github.io/Porfolio-Pro/static/';
            
            if (cssLinks.length > 0) {
                const firstCss = cssLinks[0].href;
                if (firstCss.includes(expectedAssetPath)) {
                    results.push('✅ CSS assets have correct GitHub Pages path');
                } else {
                    results.push(`❌ CSS path issue: ${firstCss}`);
                }
            }
            
            if (jsScripts.length > 0) {
                const firstJs = jsScripts[0].src;
                if (firstJs.includes(expectedAssetPath)) {
                    results.push('✅ JS assets have correct GitHub Pages path');
                } else {
                    results.push(`❌ JS path issue: ${firstJs}`);
                }
            }
            
            assetDiv.innerHTML = results.map(r => `<div class="url-test">${r}</div>`).join('');
            
            const successCount = results.filter(r => r.includes('✅')).length;
            if (successCount >= 2) {
                addResult('success', '✅ Asset Loading', 'Assets are loading with correct paths');
            } else {
                addResult('error', '❌ Asset Loading', 'Asset path issues detected');
            }
        }

        function testSPARouting() {
            const routingDiv = document.getElementById('routing-test');
            routingDiv.innerHTML = '<div class="info">Testing SPA routing...</div>';
            
            // Check if we have the GitHub Pages SPA routing script
            const scripts = document.querySelectorAll('script');
            let hasRoutingScript = false;
            
            scripts.forEach(script => {
                if (script.innerHTML.includes('Single Page Apps for GitHub Pages')) {
                    hasRoutingScript = true;
                }
            });
            
            let results = [];
            
            if (hasRoutingScript) {
                results.push('✅ GitHub Pages SPA routing script detected');
            } else {
                results.push('❌ GitHub Pages SPA routing script missing');
            }
            
            // Check if we're on the correct domain
            if (window.location.hostname === 'aminos555.github.io') {
                results.push('✅ Deployed on correct GitHub Pages domain');
            } else {
                results.push(`ℹ️ Current domain: ${window.location.hostname}`);
            }
            
            // Check if the path structure is correct
            if (window.location.pathname.startsWith('/Porfolio-Pro')) {
                results.push('✅ Correct GitHub Pages path structure');
            } else {
                results.push(`ℹ️ Current path: ${window.location.pathname}`);
            }
            
            routingDiv.innerHTML = results.map(r => `<div class="url-test">${r}</div>`).join('');
            
            if (hasRoutingScript) {
                addResult('success', '✅ SPA Routing', 'SPA routing is properly configured');
            } else {
                addResult('error', '❌ SPA Routing', 'SPA routing configuration missing');
            }
        }

        function verifyMetaTags() {
            const metaDiv = document.getElementById('meta-verification');
            metaDiv.innerHTML = '<div class="info">Verifying meta tags...</div>';
            
            const checks = [
                {
                    selector: 'meta[property="og:image"]',
                    expected: 'aminos555.github.io/Porfolio-Pro/logo.PNG',
                    name: 'Open Graph Image'
                },
                {
                    selector: 'meta[name="twitter:image"]',
                    expected: 'aminos555.github.io/Porfolio-Pro/logo.PNG',
                    name: 'Twitter Image'
                },
                {
                    selector: 'meta[property="og:url"]',
                    expected: 'aminos555.github.io/Porfolio-Pro/',
                    name: 'Open Graph URL'
                }
            ];
            
            let results = [];
            let passedChecks = 0;
            
            checks.forEach(check => {
                const element = document.querySelector(check.selector);
                if (element && element.content.includes(check.expected)) {
                    results.push(`✅ ${check.name}: ${element.content}`);
                    passedChecks++;
                } else {
                    results.push(`❌ ${check.name}: ${element ? element.content : 'Not found'}`);
                }
            });
            
            metaDiv.innerHTML = results.map(r => `<div class="url-test">${r}</div>`).join('');
            
            if (passedChecks === checks.length) {
                addResult('success', '✅ Meta Tags', 'All social media meta tags are correctly updated');
            } else {
                addResult('error', '❌ Meta Tags', `${passedChecks}/${checks.length} meta tags are correct`);
            }
        }

        // Auto-run tests when page loads
        window.onload = () => {
            setTimeout(() => {
                addResult('info', '🔄 Auto-Test', 'Starting deployment verification tests...');
                setTimeout(testDeploymentStatus, 1000);
                setTimeout(testAssetLoading, 3000);
                setTimeout(testSPARouting, 5000);
                setTimeout(verifyMetaTags, 7000);
            }, 1000);
        };
    </script>
</body>
</html>
