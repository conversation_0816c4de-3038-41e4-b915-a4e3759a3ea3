<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Geolocation & Flags</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .loading { color: #ff9800; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .flag-test {
            font-size: 24px;
            margin: 10px 0;
        }
        .api-info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Geolocation & Flags</h1>
    <p>This page tests geolocation API and flag display issues.</p>

    <div class="api-info">
        <h3>📋 API Information</h3>
        <p><strong>Service:</strong> ipapi.co (Free tier)</p>
        <p><strong>Rate Limit:</strong> 1,000 requests per month for free accounts</p>
        <p><strong>Rate Limit Per IP:</strong> ~30 requests per minute</p>
        <p><strong>Common Issues:</strong></p>
        <ul>
            <li>Rate limiting returns HTML instead of JSON</li>
            <li>Free tier has monthly quota limits</li>
            <li>Multiple rapid requests can trigger temporary blocks</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>1. Direct API Test</h2>
        <button onclick="testDirectAPI()">Test ipapi.co Directly</button>
        <div id="direct-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Backend Geolocation Test</h2>
        <button onclick="testBackendGeo()">Test Backend Geolocation</button>
        <div id="backend-geo-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Flag Generation Test</h2>
        <button onclick="testFlagGeneration()">Test Flag Generation</button>
        <div id="flag-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Current Visitor Data Test</h2>
        <button onclick="testCurrentVisitors()">Check Current Visitor Flags</button>
        <div id="visitors-result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:5000';

        async function getAuthToken() {
            let token = localStorage.getItem('token');
            if (!token) {
                try {
                    const response = await fetch(`${API_URL}/api/admin/login`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'Adminboss'
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        token = data.token;
                        localStorage.setItem('token', token);
                    }
                } catch (error) {
                    console.error('Login error:', error);
                }
            }
            return token;
        }

        async function testDirectAPI() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.innerHTML = '<div class="loading">Testing direct API...</div>';
            
            try {
                // Test with a known IP
                const testIP = '*******';
                const response = await fetch(`https://ipapi.co/${testIP}/json/`);
                const text = await response.text();
                
                if (text.startsWith('<')) {
                    resultDiv.innerHTML = `
                        <div class="error">❌ API Rate Limited!</div>
                        <div class="warning">The API returned HTML instead of JSON, indicating rate limiting.</div>
                        <div class="info">This is why flags show as 🌍 - the geolocation service is temporarily blocked.</div>
                    `;
                } else {
                    const data = JSON.parse(text);
                    if (data.error) {
                        resultDiv.innerHTML = `
                            <div class="error">❌ API Error: ${data.reason}</div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="success">✅ API Working!</div>
                            <div class="info">IP: ${testIP}</div>
                            <div class="info">Country: ${data.country_name} (${data.country_code})</div>
                            <div class="info">City: ${data.city}</div>
                            <div class="flag-test">Flag: ${generateFlag(data.country_code)}</div>
                        `;
                    }
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Direct API test failed!</div>
                    <div class="error">Error: ${error.message}</div>
                `;
            }
        }

        async function testBackendGeo() {
            const resultDiv = document.getElementById('backend-geo-result');
            resultDiv.innerHTML = '<div class="loading">Testing backend geolocation...</div>';
            
            try {
                const token = await getAuthToken();
                const testIPs = ['*******', '*******'];
                
                const response = await fetch(`${API_URL}/api/admin/geolocation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ ips: testIPs })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                
                let resultHTML = `<div class="success">✅ Backend geolocation working!</div>`;
                Object.entries(data.data).forEach(([ip, geo]) => {
                    const status = geo.error ? '❌' : '✅';
                    const flag = geo.flag || '🌍';
                    resultHTML += `
                        <div class="info">${status} ${ip}: ${geo.country} ${flag}</div>
                        ${geo.error ? `<div class="warning">Error: ${geo.errorMessage}</div>` : ''}
                    `;
                });
                
                resultDiv.innerHTML = resultHTML;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Backend geolocation failed!</div>
                    <div class="error">Error: ${error.message}</div>
                `;
            }
        }

        function generateFlag(countryCode) {
            if (!countryCode || countryCode.length !== 2) return '🌍';
            
            const codePoints = countryCode
                .toUpperCase()
                .split('')
                .map(char => 127397 + char.charCodeAt());
            
            return String.fromCodePoint(...codePoints);
        }

        function testFlagGeneration() {
            const resultDiv = document.getElementById('flag-result');
            
            const testCodes = ['TN', 'US', 'FR', 'DE', 'GB', 'JP', 'AU', 'CA'];
            let resultHTML = '<div class="success">✅ Flag generation test:</div>';
            
            testCodes.forEach(code => {
                const flag = generateFlag(code);
                resultHTML += `<div class="flag-test">${code}: ${flag}</div>`;
            });
            
            resultDiv.innerHTML = resultHTML;
        }

        async function testCurrentVisitors() {
            const resultDiv = document.getElementById('visitors-result');
            resultDiv.innerHTML = '<div class="loading">Checking current visitor data...</div>';
            
            try {
                const token = await getAuthToken();
                
                const response = await fetch(`${API_URL}/api/admin/dashboard`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                const uniqueIPs = [...new Set(data.visits.map(v => v.ip))].slice(0, 5);
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Found ${uniqueIPs.length} unique IPs (showing first 5)</div>
                    <div class="info">Sample IPs: ${uniqueIPs.join(', ')}</div>
                    <div class="warning">Note: These IPs will be processed by the geolocation service when you visit All Visitors page</div>
                    <div class="info">If you see 🌍 flags, it means the API is rate limited or quota exceeded</div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Failed to get visitor data!</div>
                    <div class="error">Error: ${error.message}</div>
                `;
            }
        }

        // Auto-test flag generation on page load
        window.onload = () => {
            testFlagGeneration();
        };
    </script>
</body>
</html>
