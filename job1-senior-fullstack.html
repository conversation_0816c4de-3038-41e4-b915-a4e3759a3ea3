<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="logo.PNG">
    <title>Full Stack Developer - TechCorp Solutions | Portfolio</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="job-detail.css">
    <!-- Google Fonts for Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <a href="index.html">
                <img src="logo.PNG" alt="Logo" class="logo-img">
            </a>
        </div>
        <a href="https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93" class="cv-button">Get CV</a>
    </header>

    <!-- Navigation Back -->
    <div class="back-navigation">
        <a href="index.html#experience" class="back-button">
            <span class="back-arrow">←</span>
            <span>Back to Timeline</span>
        </a>
    </div>

    <!-- Job Detail Hero Section -->
    <section class="job-hero">
        <div class="job-hero-content">
            <div class="company-branding">
                <img src="https://via.placeholder.com/120x120/4B0082/FFFFFF?text=TC" alt="TechCorp Solutions Logo" class="hero-company-logo">
                <div class="company-info">
                    <h1 class="job-title-hero">Full Stack Developer</h1>
                    <h2 class="company-name-hero">TechCorp Solutions</h2>
                    <p class="job-duration-hero">2023 - Present</p>
                </div>
            </div>
            <div class="job-summary">
                <p>Leading development of scalable web applications using React, Node.js, and modern cloud technologies. Mentoring junior developers and implementing best practices for code quality and performance optimization.</p>
            </div>
        </div>
    </section>

    <!-- Job Details Content -->
    <section class="job-content">
        <div class="content-grid">
            <!-- Full Job Description -->
            <div class="content-card">
                <h3>Role Overview</h3>
                <p>Full Stack Developer at TechCorp Solutions, I lead the development of enterprise-level web applications that serve thousands of users daily. My role encompasses both technical leadership and hands-on development, ensuring our products meet the highest standards of performance, security, and user experience.</p>
                
                <h4>Key Responsibilities</h4>
                <ul>
                    <li>Architect and develop scalable web applications using React.js and Node.js</li>
                    <li>Lead a team of 5 junior developers, providing mentorship and code reviews</li>
                    <li>Implement CI/CD pipelines and DevOps best practices</li>
                    <li>Collaborate with product managers and designers to deliver user-centric solutions</li>
                    <li>Optimize application performance and ensure 99.9% uptime</li>
                    <li>Conduct technical interviews and contribute to hiring decisions</li>
                </ul>
            </div>

            <!-- Skills & Technologies -->
            <div class="content-card">
                <h3>Technologies & Skills</h3>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h4>Frontend</h4>
                        <div class="skill-tags">
                            <span class="skill-tag">React.js</span>
                            <span class="skill-tag">TypeScript</span>
                            <span class="skill-tag">Next.js</span>
                            <span class="skill-tag">Redux</span>
                            <span class="skill-tag">Tailwind CSS</span>
                        </div>
                    </div>
                    <div class="skill-category">
                        <h4>Backend</h4>
                        <div class="skill-tags">
                            <span class="skill-tag">Node.js</span>
                            <span class="skill-tag">Express.js</span>
                            <span class="skill-tag">PostgreSQL</span>
                            <span class="skill-tag">MongoDB</span>
                            <span class="skill-tag">GraphQL</span>
                        </div>
                    </div>
                    <div class="skill-category">
                        <h4>Cloud & DevOps</h4>
                        <div class="skill-tags">
                            <span class="skill-tag">AWS</span>
                            <span class="skill-tag">Docker</span>
                            <span class="skill-tag">Kubernetes</span>
                            <span class="skill-tag">Jenkins</span>
                            <span class="skill-tag">Terraform</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Accomplishments -->
            <div class="content-card">
                <h3>Key Accomplishments</h3>
                <div class="accomplishments-list">
                    <div class="accomplishment-item">
                        <div class="metric">40%</div>
                        <div class="metric-description">Improved application performance through code optimization and caching strategies</div>
                    </div>
                    <div class="accomplishment-item">
                        <div class="metric">99.9%</div>
                        <div class="metric-description">Achieved system uptime through robust architecture and monitoring</div>
                    </div>
                    <div class="accomplishment-item">
                        <div class="metric">5</div>
                        <div class="metric-description">Junior developers successfully mentored and promoted</div>
                    </div>

                </div>
            </div>
        </div>
    </section>

    <!-- Project Portfolio from this role -->
    <section class="role-projects">
        <h2>Projects from this Role</h2>
        <div class="projects-grid">
            <div class="project-card">
                <div class="project-image">
                    <img src="https://via.placeholder.com/400x250/4B0082/FFFFFF?text=E-Commerce+Platform" alt="E-Commerce Platform">
                </div>
                <div class="project-info">
                    <h3>Enterprise E-Commerce Platform</h3>
                    <p>Led development of a scalable e-commerce solution handling 10K+ daily transactions</p>
                    <div class="project-tech">
                        <span>React</span>
                        <span>Node.js</span>
                        <span>AWS</span>
                    </div>
                </div>
            </div>
            
            <div class="project-card">
                <div class="project-image">
                    <img src="https://via.placeholder.com/400x250/FF2D55/FFFFFF?text=Analytics+Dashboard" alt="Analytics Dashboard">
                </div>
                <div class="project-info">
                    <h3>Real-time Analytics Dashboard</h3>
                    <p>Built comprehensive analytics platform with real-time data visualization</p>
                    <div class="project-tech">
                        <span>React</span>
                        <span>D3.js</span>
                        <span>WebSocket</span>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Footer -->
    <footer>
        <p>2025 REECRAFT. ALL RIGHTS RESERVED.</p>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top">↑</a>
</body>
</html>
