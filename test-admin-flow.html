<!DOCTYPE html>
<html>
<head>
    <title>Admin Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px 15px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        #results { margin-top: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .test-result.success { border-left-color: green; background: #f0fff0; }
        .test-result.error { border-left-color: red; background: #fff0f0; }
        .test-result.info { border-left-color: blue; background: #f0f0ff; }
    </style>
</head>
<body>
    <h1>🧪 Admin Dashboard & All Visitors Details - Complete Test Suite</h1>
    
    <div class="test-section">
        <h2>🔧 Test Controls</h2>
        <button onclick="runCompleteTest()">🚀 Run Complete Test Suite</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
        <button onclick="testSpecificFeature()">🎯 Test Show All Feature</button>
    </div>

    <div id="results"></div>

    <script>
        const API_URL = 'https://porfolio-pro-backend.onrender.com';
        let authToken = null;
        let testResults = [];

        function addResult(type, title, message, details = '') {
            const result = { type, title, message, details, timestamp: new Date().toLocaleTimeString() };
            testResults.push(result);
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>📊 Test Results</h2>' + 
                testResults.map(result => `
                    <div class="test-result ${result.type}">
                        <strong>[${result.timestamp}] ${result.title}</strong><br>
                        ${result.message}
                        ${result.details ? `<br><small>${result.details}</small>` : ''}
                    </div>
                `).join('');
        }

        function clearResults() {
            testResults = [];
            updateResultsDisplay();
        }

        async function testBackendConnection() {
            addResult('info', '🔌 Backend Connection', 'Testing backend connectivity...');
            
            try {
                const response = await fetch(`${API_URL}/api/ping`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                addResult('success', '✅ Backend Connection', 'Backend is accessible and responding', 
                    `Response: ${JSON.stringify(data)}`);
                return true;
            } catch (error) {
                addResult('error', '❌ Backend Connection', 'Failed to connect to backend', 
                    `Error: ${error.message}`);
                return false;
            }
        }

        async function testAdminLogin() {
            addResult('info', '🔐 Admin Login', 'Testing admin authentication...');
            
            try {
                const response = await fetch(`${API_URL}/api/admin/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        email: '<EMAIL>', 
                        password: 'Adminboss' 
                    })
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.token) {
                    authToken = data.token;
                    addResult('success', '✅ Admin Login', 'Successfully authenticated as admin');
                    return true;
                } else {
                    addResult('error', '❌ Admin Login', 'Login failed - no token received', 
                        `Response: ${JSON.stringify(data)}`);
                    return false;
                }
            } catch (error) {
                addResult('error', '❌ Admin Login', 'Login request failed', 
                    `Error: ${error.message}`);
                return false;
            }
        }

        async function testVisitorDataFetch() {
            addResult('info', '👥 Visitor Data', 'Fetching visitor data from dashboard...');
            
            if (!authToken) {
                addResult('error', '❌ Visitor Data', 'No auth token available - login first');
                return false;
            }
            
            try {
                const response = await fetch(`${API_URL}/api/admin/dashboard`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                const visitCount = data.visits ? data.visits.length : 0;
                const uniqueVisitors = data.uniqueVisitors || 0;
                
                // Test the main fix: no more 20-visit limit
                if (visitCount > 20) {
                    addResult('success', '✅ Visitor Limit Fix', 
                        `Successfully fetched ${visitCount} visits (more than old 20-visit limit)`,
                        `Unique visitors: ${uniqueVisitors}`);
                } else if (visitCount > 0) {
                    addResult('info', 'ℹ️ Visitor Limit Fix', 
                        `Fetched ${visitCount} visits (limit removed but need more test data)`,
                        `Unique visitors: ${uniqueVisitors}`);
                } else {
                    addResult('warning', '⚠️ Visitor Data', 'No visitor data found in database');
                }
                
                // Test visitor data structure
                if (data.visits && data.visits.length > 0) {
                    const sampleVisit = data.visits[0];
                    const hasRequiredFields = sampleVisit.ip && sampleVisit.timestamp && sampleVisit.section;
                    
                    if (hasRequiredFields) {
                        addResult('success', '✅ Visitor Data Structure', 
                            'Visitor records contain required fields (ip, timestamp, section)');
                    } else {
                        addResult('error', '❌ Visitor Data Structure', 
                            'Visitor records missing required fields');
                    }
                }
                
                return { visitCount, uniqueVisitors, visits: data.visits };
            } catch (error) {
                addResult('error', '❌ Visitor Data', 'Failed to fetch visitor data', 
                    `Error: ${error.message}`);
                return false;
            }
        }

        function testTimezoneFormatting() {
            addResult('info', '🌍 Timezone Test', 'Testing Tunisia timezone formatting...');
            
            try {
                const testDate = new Date();
                const tunisiaTime = testDate.toLocaleString('en-US', {
                    timeZone: 'Africa/Tunis',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const utcTime = testDate.toISOString();
                const localTime = testDate.toLocaleString();
                
                addResult('success', '✅ Tunisia Timezone', 
                    `Timezone formatting working correctly`,
                    `UTC: ${utcTime}<br>Local: ${localTime}<br>Tunisia: ${tunisiaTime}`);
                return true;
            } catch (error) {
                addResult('error', '❌ Tunisia Timezone', 'Timezone formatting failed', 
                    `Error: ${error.message}`);
                return false;
            }
        }

        function testMetaTagsUpdate() {
            addResult('info', '🏷️ Meta Tags', 'Checking social media meta tags...');
            
            try {
                const checks = [
                    {
                        selector: 'meta[property="og:image"]',
                        expected: 'aminos555.github.io/Porfolio-Pro/logo.PNG',
                        name: 'Open Graph Image'
                    },
                    {
                        selector: 'meta[name="twitter:image"]',
                        expected: 'aminos555.github.io/Porfolio-Pro/logo.PNG',
                        name: 'Twitter Image'
                    },
                    {
                        selector: 'meta[property="og:url"]',
                        expected: 'aminos555.github.io/Porfolio-Pro/',
                        name: 'Open Graph URL'
                    }
                ];
                
                let passedChecks = 0;
                let details = [];
                
                checks.forEach(check => {
                    const element = document.querySelector(check.selector);
                    if (element && element.content.includes(check.expected)) {
                        passedChecks++;
                        details.push(`✅ ${check.name}: ${element.content}`);
                    } else {
                        details.push(`❌ ${check.name}: ${element ? element.content : 'Not found'}`);
                    }
                });
                
                if (passedChecks === checks.length) {
                    addResult('success', '✅ Meta Tags Update', 
                        'All social media meta tags updated correctly',
                        details.join('<br>'));
                } else {
                    addResult('error', '❌ Meta Tags Update', 
                        `${passedChecks}/${checks.length} meta tags updated correctly`,
                        details.join('<br>'));
                }
                
                return passedChecks === checks.length;
            } catch (error) {
                addResult('error', '❌ Meta Tags Update', 'Meta tags check failed', 
                    `Error: ${error.message}`);
                return false;
            }
        }

        async function testSpecificFeature() {
            addResult('info', '🎯 Show All Feature', 'Testing Show All visitors functionality...');
            
            // This would need to be tested in the actual React app
            // For now, we'll test the logic components
            
            const mockVisitors = [
                { ip: '***********', lastVisit: new Date('2024-01-15'), country: 'Tunisia' },
                { ip: '***********', lastVisit: new Date('2024-01-14'), country: 'France' },
                { ip: '***********', lastVisit: new Date('2024-01-16'), country: 'Tunisia' }
            ];
            
            // Test sorting by newest first (Show All behavior)
            const sortedByNewest = [...mockVisitors].sort((a, b) => new Date(b.lastVisit) - new Date(a.lastVisit));
            const expectedOrder = ['***********', '***********', '***********'];
            const actualOrder = sortedByNewest.map(v => v.ip);
            
            if (JSON.stringify(actualOrder) === JSON.stringify(expectedOrder)) {
                addResult('success', '✅ Show All Sorting', 
                    'Show All feature sorting logic works correctly',
                    `Order: ${actualOrder.join(' → ')}`);
            } else {
                addResult('error', '❌ Show All Sorting', 
                    'Show All feature sorting logic failed',
                    `Expected: ${expectedOrder.join(' → ')}<br>Actual: ${actualOrder.join(' → ')}`);
            }
        }

        async function runCompleteTest() {
            clearResults();
            addResult('info', '🚀 Test Suite Started', 'Running complete test suite...');
            
            // Test 1: Backend Connection
            const backendOk = await testBackendConnection();
            if (!backendOk) {
                addResult('error', '🛑 Test Suite Stopped', 'Cannot continue without backend connection');
                return;
            }
            
            // Test 2: Admin Login
            const loginOk = await testAdminLogin();
            if (!loginOk) {
                addResult('error', '🛑 Test Suite Stopped', 'Cannot continue without admin authentication');
                return;
            }
            
            // Test 3: Visitor Data (main fix)
            const visitorData = await testVisitorDataFetch();
            
            // Test 4: Timezone Formatting
            testTimezoneFormatting();
            
            // Test 5: Meta Tags
            testMetaTagsUpdate();
            
            // Test 6: Show All Feature Logic
            await testSpecificFeature();
            
            // Summary
            const successCount = testResults.filter(r => r.type === 'success').length;
            const errorCount = testResults.filter(r => r.type === 'error').length;
            const totalTests = testResults.filter(r => r.title.includes('✅') || r.title.includes('❌')).length;
            
            if (errorCount === 0) {
                addResult('success', '🎉 Test Suite Complete', 
                    `All ${successCount} tests passed successfully!`,
                    'All fixes are working correctly and ready for deployment.');
            } else {
                addResult('error', '⚠️ Test Suite Complete', 
                    `${successCount}/${totalTests} tests passed, ${errorCount} failed`,
                    'Some issues need to be addressed before deployment.');
            }
        }

        // Auto-run tests when page loads
        window.onload = () => {
            setTimeout(() => {
                addResult('info', '🔄 Auto-Test', 'Starting automatic test suite in 2 seconds...');
                setTimeout(runCompleteTest, 2000);
            }, 1000);
        };
    </script>
</body>
</html>
