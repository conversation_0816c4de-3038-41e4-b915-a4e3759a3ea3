<!DOCTYPE html>
<html>
<head>
    <title>Backend Test</title>
</head>
<body>
    <h1>Backend Connection Test</h1>
    <button onclick="testBackend()">Test Backend</button>
    <div id="result"></div>

    <script>
        async function testBackend() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('https://porfolio-pro-backend.onrender.com/api/ping');
                const data = await response.json();
                resultDiv.innerHTML = `<p style="color: green;">Backend is running! Response: ${JSON.stringify(data)}</p>`;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">Backend connection failed: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
