require('dotenv').config();
const mongoose = require('mongoose');

// Define the Visit schema
const visitSchema = new mongoose.Schema({
  ip: { type: String, required: true },
  section: { type: String, required: true },
  duration: { type: Number, default: 0 },
  timestamp: { type: Date, default: Date.now },
  sessionId: String,
  pageUrl: String,
  userAgent: String,
  referrer: String,
  jobTitle: String,
  jobSlug: String,
  projectTitle: String,
  interactionType: String,
  cardTitle: String,
  skillCategory: String
});

const Visit = mongoose.model('Visit', visitSchema);

async function testDailyStats() {
  console.log('🧪 Testing Daily Visitor Statistics');
  console.log('===================================\n');

  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB\n');

    // Calculate daily visitor statistics
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get today's unique visitors
    const todayUniqueVisitors = await Visit.distinct('ip', {
      timestamp: { $gte: today, $lt: tomorrow }
    });
    const todayVisitorCount = todayUniqueVisitors.length;

    // Get yesterday's unique visitors for comparison
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayUniqueVisitors = await Visit.distinct('ip', {
      timestamp: { $gte: yesterday, $lt: today }
    });
    const yesterdayVisitorCount = yesterdayUniqueVisitors.length;

    console.log('📊 Daily Statistics:');
    console.log(`Today's visitors: ${todayVisitorCount}`);
    console.log(`Yesterday's visitors: ${yesterdayVisitorCount}`);
    console.log(`Today's unique IPs: ${todayUniqueVisitors.join(', ')}`);
    console.log(`Yesterday's unique IPs: ${yesterdayUniqueVisitors.join(', ')}\n`);

    // Calculate daily visitor statistics for the last 7 days
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const dailyStats = await Visit.aggregate([
      {
        $match: {
          timestamp: { $gte: sevenDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          uniqueVisitors: { $addToSet: '$ip' },
          totalVisits: { $sum: 1 }
        }
      },
      {
        $addFields: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          uniqueVisitorCount: { $size: '$uniqueVisitors' }
        }
      },
      {
        $sort: { date: -1 }
      }
    ]);

    console.log('📈 Last 7 Days Breakdown:');
    dailyStats.forEach(stat => {
      console.log(`${stat.date.toDateString()}: ${stat.uniqueVisitorCount} unique visitors, ${stat.totalVisits} total visits`);
    });

    console.log('\n✅ Daily statistics test completed successfully!');

  } catch (error) {
    console.error('❌ Error testing daily statistics:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

testDailyStats();
