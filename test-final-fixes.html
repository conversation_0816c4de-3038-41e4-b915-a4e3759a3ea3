<!DOCTYPE html>
<html>
<head>
    <title>Final Fixes Test - Country Flags & Visitor Count</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
        }
        .test-section { 
            margin: 25px 0; 
            padding: 20px; 
            border: 1px solid rgba(255,255,255,0.2); 
            border-radius: 10px; 
            background: rgba(255,255,255,0.05);
        }
        .success { color: #4ade80; font-weight: bold; }
        .error { color: #f87171; font-weight: bold; }
        .info { color: #60a5fa; }
        .warning { color: #fbbf24; }
        button { 
            margin: 8px; 
            padding: 12px 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-weight: 600;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .test-result { 
            margin: 15px 0; 
            padding: 15px; 
            border-left: 4px solid #ccc; 
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .test-result.success { border-left-color: #4ade80; }
        .test-result.error { border-left-color: #f87171; }
        .test-result.info { border-left-color: #60a5fa; }
        .test-result.warning { border-left-color: #fbbf24; }
        .country-flag {
            font-size: 2rem;
            margin-right: 10px;
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", sans-serif;
        }
        .visitor-sample {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 3px solid #60a5fa;
        }
        .flag-test {
            display: inline-block;
            margin: 5px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Final Fixes Test Suite</h1>
        <h2>Country Flags & Visitor Count Issues</h2>
        
        <div class="test-section">
            <h3>🏳️ Country Flag Generation Test</h3>
            <p>Testing the flag generation function with Tunisia and other countries:</p>
            <div id="flag-test-results"></div>
            <button onclick="testFlagGeneration()">Test Flag Generation</button>
        </div>

        <div class="test-section">
            <h3>👥 Visitor Data Analysis</h3>
            <p>Analyzing why only 3 visitors are showing:</p>
            <div id="visitor-analysis"></div>
            <button onclick="analyzeVisitorData()">Analyze Visitor Data</button>
        </div>

        <div class="test-section">
            <h3>🔍 Backend Data Investigation</h3>
            <p>Checking what data is actually in the backend:</p>
            <div id="backend-investigation"></div>
            <button onclick="investigateBackendData()">Investigate Backend</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_URL = 'https://porfolio-pro-backend.onrender.com';
        let authToken = null;
        let testResults = [];

        function addResult(type, title, message, details = '') {
            const result = { 
                type, 
                title, 
                message, 
                details, 
                timestamp: new Date().toLocaleString('en-US', { timeZone: 'Africa/Tunis' })
            };
            testResults.push(result);
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>📊 Test Results</h3>' + 
                testResults.map(result => `
                    <div class="test-result ${result.type}">
                        <strong>[${result.timestamp}] ${result.title}</strong><br>
                        ${result.message}
                        ${result.details ? `<br><small>${result.details}</small>` : ''}
                    </div>
                `).join('');
        }

        // Generate country flag from country code
        function getCountryFlag(countryCode) {
            if (!countryCode || countryCode.length !== 2) return '🌍';
            
            const codePoints = countryCode
                .toUpperCase()
                .split('')
                .map(char => 127397 + char.charCodeAt());
            
            return String.fromCodePoint(...codePoints);
        }

        function testFlagGeneration() {
            const testDiv = document.getElementById('flag-test-results');
            
            const testCodes = [
                { code: 'TN', name: 'Tunisia' },
                { code: 'FR', name: 'France' },
                { code: 'US', name: 'United States' },
                { code: 'DE', name: 'Germany' },
                { code: 'GB', name: 'United Kingdom' },
                { code: 'CA', name: 'Canada' }
            ];
            
            let html = '<h4>Generated Flags:</h4>';
            testCodes.forEach(({ code, name }) => {
                const flag = getCountryFlag(code);
                html += `
                    <div class="flag-test">
                        <span class="country-flag">${flag}</span> ${code} - ${name}
                    </div>
                `;
            });
            
            // Test the country name mapping
            const countryToCode = {
                'Tunisia': 'TN',
                'France': 'FR',
                'United States': 'US',
                'Germany': 'DE',
                'United Kingdom': 'GB',
                'Canada': 'CA'
            };
            
            html += '<h4>Country Name to Flag Mapping:</h4>';
            Object.entries(countryToCode).forEach(([country, code]) => {
                const flag = getCountryFlag(code);
                html += `
                    <div class="flag-test">
                        <span class="country-flag">${flag}</span> ${country} → ${code}
                    </div>
                `;
            });
            
            testDiv.innerHTML = html;
            
            addResult('success', '✅ Flag Generation', 'Flag generation function is working correctly');
        }

        async function authenticateAdmin() {
            if (authToken) return true;
            
            try {
                const response = await fetch(`${API_URL}/api/admin/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        email: '<EMAIL>', 
                        password: 'Adminboss' 
                    })
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.token) {
                    authToken = data.token;
                    return true;
                }
                return false;
            } catch (error) {
                addResult('error', '❌ Authentication', `Failed to authenticate: ${error.message}`);
                return false;
            }
        }

        async function analyzeVisitorData() {
            const analysisDiv = document.getElementById('visitor-analysis');
            
            if (!await authenticateAdmin()) return;
            
            try {
                const response = await fetch(`${API_URL}/api/admin/dashboard`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                
                // Analyze the data
                const totalVisits = data.visits ? data.visits.length : 0;
                const uniqueIPs = data.visits ? [...new Set(data.visits.map(v => v.ip))] : [];
                const uniqueVisitors = data.uniqueVisitors || 0;
                
                let html = `
                    <div class="visitor-sample">
                        <h4>📊 Data Analysis Results:</h4>
                        <p><strong>Total visits in database:</strong> ${totalVisits}</p>
                        <p><strong>Unique IPs found:</strong> ${uniqueIPs.length}</p>
                        <p><strong>Backend reported unique visitors:</strong> ${uniqueVisitors}</p>
                    </div>
                `;
                
                if (uniqueIPs.length > 0) {
                    html += '<h4>🔍 Unique IP Addresses:</h4>';
                    uniqueIPs.forEach(ip => {
                        const visitsForIP = data.visits.filter(v => v.ip === ip);
                        html += `
                            <div class="visitor-sample">
                                <strong>IP:</strong> ${ip}<br>
                                <strong>Visits:</strong> ${visitsForIP.length}<br>
                                <strong>Sections:</strong> ${[...new Set(visitsForIP.map(v => v.section))].join(', ')}<br>
                                <strong>Latest visit:</strong> ${new Date(visitsForIP[visitsForIP.length - 1].timestamp).toLocaleString('en-US', { timeZone: 'Africa/Tunis' })}
                            </div>
                        `;
                    });
                }
                
                analysisDiv.innerHTML = html;
                
                if (uniqueIPs.length === 3) {
                    addResult('info', 'ℹ️ Visitor Count', 
                        'Only 3 unique visitors found in database - this is the actual data, not a limit issue',
                        `The backend is correctly returning all ${totalVisits} visits from ${uniqueIPs.length} unique IPs`);
                } else {
                    addResult('success', '✅ Visitor Count', 
                        `Found ${uniqueIPs.length} unique visitors with ${totalVisits} total visits`);
                }
                
            } catch (error) {
                addResult('error', '❌ Visitor Analysis', `Analysis failed: ${error.message}`);
            }
        }

        async function investigateBackendData() {
            const investigationDiv = document.getElementById('backend-investigation');
            
            if (!await authenticateAdmin()) return;
            
            try {
                // Test the ping endpoint
                const pingResponse = await fetch(`${API_URL}/api/ping`);
                const pingData = await pingResponse.json();
                
                // Test the dashboard endpoint
                const dashboardResponse = await fetch(`${API_URL}/api/admin/dashboard`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                const dashboardData = await dashboardResponse.json();
                
                let html = `
                    <div class="visitor-sample">
                        <h4>🔌 Backend Connection:</h4>
                        <p><strong>Status:</strong> ✅ Connected</p>
                        <p><strong>Server Time:</strong> ${pingData.timestamp}</p>
                    </div>
                    
                    <div class="visitor-sample">
                        <h4>📊 Dashboard Data Structure:</h4>
                        <p><strong>Available keys:</strong> ${Object.keys(dashboardData).join(', ')}</p>
                        <p><strong>Visits array length:</strong> ${dashboardData.visits ? dashboardData.visits.length : 'N/A'}</p>
                        <p><strong>Unique visitors count:</strong> ${dashboardData.uniqueVisitors || 'N/A'}</p>
                    </div>
                `;
                
                if (dashboardData.visits && dashboardData.visits.length > 0) {
                    const sampleVisit = dashboardData.visits[0];
                    html += `
                        <div class="visitor-sample">
                            <h4>📝 Sample Visit Structure:</h4>
                            <pre>${JSON.stringify(sampleVisit, null, 2)}</pre>
                        </div>
                    `;
                }
                
                investigationDiv.innerHTML = html;
                
                addResult('success', '✅ Backend Investigation', 
                    'Backend is responding correctly with all available data',
                    `Found ${dashboardData.visits ? dashboardData.visits.length : 0} visits in database`);
                
            } catch (error) {
                addResult('error', '❌ Backend Investigation', `Investigation failed: ${error.message}`);
            }
        }

        // Auto-run tests when page loads
        window.onload = () => {
            setTimeout(() => {
                testFlagGeneration();
                setTimeout(analyzeVisitorData, 2000);
                setTimeout(investigateBackendData, 4000);
            }, 1000);
        };
    </script>
</body>
</html>
