<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Analytics Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>Portfolio Analytics Frontend Test</h1>
    
    <div class="test-section">
        <h2>Test Visitor Tracking API</h2>
        <p>This will test if the visitor tracking endpoints are working correctly.</p>
        
        <button class="test-button" onclick="testVisitTracking()">Test Visit Tracking</button>
        <button class="test-button" onclick="testAdminLogin()">Test Admin Login</button>
        <button class="test-button" onclick="testDashboardData()">Test Dashboard Data</button>
        <button class="test-button" onclick="testSectionDetails()">Test Section Details</button>
        <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        
        <div id="results" class="results">Ready to run tests...</div>
    </div>

    <div class="test-section">
        <h2>Simulate User Behavior</h2>
        <p>This will simulate a user browsing different sections with realistic durations.</p>
        
        <button class="test-button" onclick="simulateUserSession()">Simulate User Session</button>
        <button class="test-button" onclick="simulateJobDetailVisit()">Simulate Job Detail Visit</button>
        
        <div id="simulation-results" class="results">Ready to simulate user behavior...</div>
    </div>

    <script>
        const API_URL = 'https://porfolio-pro-backend.onrender.com';
        let adminToken = null;

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            results.innerHTML += `[${timestamp}] <span class="${className}">${message}</span>\n`;
            results.scrollTop = results.scrollHeight;
        }

        function logSimulation(message, type = 'info') {
            const results = document.getElementById('simulation-results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            results.innerHTML += `[${timestamp}] <span class="${className}">${message}</span>\n`;
            results.scrollTop = results.scrollHeight;
        }

        async function testVisitTracking() {
            log('Testing visit tracking...');
            try {
                const response = await fetch(`${API_URL}/api/track/visit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        section: 'test-frontend',
                        duration: 10,
                        userAgent: navigator.userAgent,
                        sessionId: 'frontend_test_' + Date.now(),
                        pageUrl: window.location.href
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    log('✅ Visit tracking successful: ' + JSON.stringify(data), 'success');
                } else {
                    log('❌ Visit tracking failed: ' + response.status, 'error');
                }
            } catch (error) {
                log('❌ Visit tracking error: ' + error.message, 'error');
            }
        }

        async function testAdminLogin() {
            log('Testing admin login...');
            try {
                const response = await fetch(`${API_URL}/api/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Adminboss'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    adminToken = data.token;
                    log('✅ Admin login successful', 'success');
                } else {
                    log('❌ Admin login failed: ' + response.status, 'error');
                }
            } catch (error) {
                log('❌ Admin login error: ' + error.message, 'error');
            }
        }

        async function testDashboardData() {
            if (!adminToken) {
                log('❌ Need to login first', 'error');
                return;
            }

            log('Testing dashboard data...');
            try {
                const response = await fetch(`${API_URL}/api/admin/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('✅ Dashboard data retrieved:', 'success');
                    log(`Total visits: ${data.totalVisits}, Unique visitors: ${data.uniqueVisitors}`);
                } else {
                    log('❌ Dashboard data failed: ' + response.status, 'error');
                }
            } catch (error) {
                log('❌ Dashboard data error: ' + error.message, 'error');
            }
        }

        async function testSectionDetails() {
            if (!adminToken) {
                log('❌ Need to login first', 'error');
                return;
            }

            log('Testing section details...');
            try {
                const response = await fetch(`${API_URL}/api/track/section-details`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('✅ Section details retrieved:', 'success');
                    log(`Total visitors in details: ${data.totalVisitors}`);
                    if (data.data.length > 0) {
                        const firstVisitor = data.data[0];
                        log(`First visitor (${firstVisitor.ip}) spent ${firstVisitor.totalTimeSpent}s total`);
                    }
                } else {
                    log('❌ Section details failed: ' + response.status, 'error');
                }
            } catch (error) {
                log('❌ Section details error: ' + error.message, 'error');
            }
        }

        async function runAllTests() {
            document.getElementById('results').innerHTML = 'Running all tests...\n';
            await testVisitTracking();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAdminLogin();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testDashboardData();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testSectionDetails();
            log('🎉 All tests completed!', 'success');
        }

        async function simulateUserSession() {
            logSimulation('Starting user session simulation...');
            const sessionId = 'sim_session_' + Date.now();
            
            const sections = [
                { name: 'intro', duration: 12 },
                { name: 'skills', duration: 8 },
                { name: 'experience', duration: 25 },
                { name: 'portfolio', duration: 45 },
                { name: 'contact', duration: 6 }
            ];

            for (const section of sections) {
                logSimulation(`Simulating ${section.name} section (${section.duration}s)...`);
                
                try {
                    const response = await fetch(`${API_URL}/api/track/visit`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            section: section.name,
                            duration: section.duration,
                            userAgent: navigator.userAgent,
                            sessionId: sessionId,
                            pageUrl: 'http://localhost:3001/'
                        })
                    });

                    if (response.ok) {
                        logSimulation(`✅ ${section.name}: ${section.duration}s logged`, 'success');
                    } else {
                        logSimulation(`❌ ${section.name}: Failed to log`, 'error');
                    }
                } catch (error) {
                    logSimulation(`❌ ${section.name}: ${error.message}`, 'error');
                }

                await new Promise(resolve => setTimeout(resolve, 300));
            }
            
            logSimulation('🎉 User session simulation completed!', 'success');
        }

        async function simulateJobDetailVisit() {
            logSimulation('Simulating job detail visit...');
            const sessionId = 'job_sim_' + Date.now();
            
            try {
                const response = await fetch(`${API_URL}/api/track/visit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        section: 'job-detail',
                        duration: 87, // 1 minute 27 seconds
                        userAgent: navigator.userAgent,
                        sessionId: sessionId,
                        pageUrl: 'http://localhost:3001/job/3d-ecommerce-platform-ui-ux-designer',
                        jobTitle: '3D E-commerce Platform UI/UX Designer'
                    })
                });

                if (response.ok) {
                    logSimulation('✅ Job detail visit (87s) logged successfully', 'success');
                } else {
                    logSimulation('❌ Job detail visit failed', 'error');
                }
            } catch (error) {
                logSimulation('❌ Job detail visit error: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
