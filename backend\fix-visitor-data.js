require('dotenv').config();
const mongoose = require('mongoose');

// Define the Visit schema
const visitSchema = new mongoose.Schema({
  ip: { type: String, required: true },
  section: { type: String, required: true },
  duration: { type: Number, default: 0 },
  timestamp: { type: Date, default: Date.now },
  sessionId: String,
  pageUrl: String,
  userAgent: String,
  referrer: String,
  jobTitle: String,
  jobSlug: String,
  projectTitle: String,
  interactionType: String,
  cardTitle: String,
  skillCategory: String
});

const Visit = mongoose.model('Visit', visitSchema);

async function fixVisitorData() {
  console.log('🔧 FIXING VISITOR DATA ISSUES');
  console.log('===============================\n');
  
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // 1. Check for comma-separated IPs
    console.log('\n1️⃣ Checking for comma-separated IPs...');
    const commaVisits = await Visit.find({ ip: { $regex: ',' } });
    console.log(`🔍 Found ${commaVisits.length} visits with comma-separated IPs`);
    
    if (commaVisits.length > 0) {
      console.log('📋 Sample comma-separated IPs:');
      commaVisits.slice(0, 5).forEach(visit => {
        console.log(`   - "${visit.ip}" (${visit.section})`);
      });
      
      // Fix comma-separated IPs
      console.log('\n🔧 Fixing comma-separated IPs...');
      let fixedCount = 0;
      
      for (const visit of commaVisits) {
        const originalIP = visit.ip;
        const firstIP = originalIP.split(',')[0].trim();
        
        await Visit.updateOne(
          { _id: visit._id },
          { $set: { ip: firstIP } }
        );
        
        fixedCount++;
        if (fixedCount % 10 === 0) {
          console.log(`   ✅ Fixed ${fixedCount}/${commaVisits.length} IPs`);
        }
      }
      
      console.log(`✅ Fixed ${fixedCount} comma-separated IPs`);
    } else {
      console.log('✅ No comma-separated IPs found');
    }
    
    // 2. Check for empty or invalid IPs
    console.log('\n2️⃣ Checking for empty or invalid IPs...');
    const invalidIPs = await Visit.find({
      $or: [
        { ip: { $exists: false } },
        { ip: '' },
        { ip: null },
        { ip: { $regex: /^\s*$/ } }
      ]
    });
    
    console.log(`🔍 Found ${invalidIPs.length} visits with invalid IPs`);
    
    if (invalidIPs.length > 0) {
      console.log('⚠️ Sample invalid IPs:');
      invalidIPs.slice(0, 5).forEach(visit => {
        console.log(`   - IP: "${visit.ip}" (${visit.section})`);
      });
      
      // You might want to delete these or set them to 'unknown'
      console.log('💡 Consider cleaning up these invalid IP entries');
    }
    
    // 3. Get final statistics
    console.log('\n3️⃣ Final database statistics...');
    const totalVisits = await Visit.countDocuments();
    const uniqueIPs = await Visit.distinct('ip');
    const validIPs = uniqueIPs.filter(ip => ip && ip.trim() !== '' && !ip.includes(','));
    
    console.log(`📊 Total visits: ${totalVisits}`);
    console.log(`👥 Total unique IPs: ${uniqueIPs.length}`);
    console.log(`✅ Valid unique IPs: ${validIPs.length}`);
    console.log(`🌍 Sample valid IPs: ${validIPs.slice(0, 5).join(', ')}`);
    
    // 4. Verify no comma-separated IPs remain
    const remainingCommaIPs = await Visit.find({ ip: { $regex: ',' } });
    if (remainingCommaIPs.length === 0) {
      console.log('\n🎉 SUCCESS: All comma-separated IPs have been fixed!');
    } else {
      console.log(`\n⚠️ WARNING: ${remainingCommaIPs.length} comma-separated IPs still remain`);
    }
    
    console.log('\n✅ Visitor data fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing visitor data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

fixVisitorData();
