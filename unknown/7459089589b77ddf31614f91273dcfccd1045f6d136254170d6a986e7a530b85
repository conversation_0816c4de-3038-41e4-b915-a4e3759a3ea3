.visitor-details-container {
  min-height: 100vh;
  padding: 50px 30px;
  background:
    linear-gradient(135deg, rgba(75,0,130,0.2) 0%, rgba(255,45,85,0.15) 100%),
    radial-gradient(circle at 20% 20%, rgba(75,0,130,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255,45,85,0.1) 0%, transparent 50%);
  border-radius: 35px;
  box-shadow:
    0 25px 50px rgba(75,0,130,0.25),
    0 15px 35px rgba(255,45,85,0.15),
    0 5px 15px rgba(0,0,0,0.3),
    inset 0 1px 0 rgba(255,255,255,0.1);
  font-family: 'Montserrat', sans-serif;
  color: #fff;
  position: relative;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255,255,255,0.1);
  animation: dashboardFadeIn 0.8s ease-out;
  width: 100%;
  max-width: 1400px;
  margin: 20px auto;
  overflow: hidden;
}

/* Floating particles animation */
.visitor-details-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255,45,85,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(75,0,130,0.1) 1px, transparent 1px),
    radial-gradient(circle at 50% 10%, rgba(255,255,255,0.05) 1px, transparent 1px),
    radial-gradient(circle at 10% 90%, rgba(255,45,85,0.08) 1px, transparent 1px);
  background-size: 100px 100px, 80px 80px, 120px 120px, 90px 90px;
  animation: floatingParticles 20s linear infinite;
  pointer-events: none;
  z-index: 1;
}

.visitor-details-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 80% 20%, rgba(75,0,130,0.05) 0%, transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(255,45,85,0.05) 0%, transparent 50%);
  animation: pulseGlow 4s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: 1;
}

@keyframes dashboardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes floatingParticles {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
  100% {
    transform: translateY(0px) rotate(360deg);
  }
}

@keyframes pulseGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

.visitor-details-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  position: relative;
  z-index: 10;
}

.visitor-details-header h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-size: 1rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow:
    0 6px 20px rgba(255,45,85,0.3),
    0 3px 10px rgba(75,0,130,0.2),
    inset 0 1px 0 rgba(255,255,255,0.15);
  cursor: pointer;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.back-button:hover {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  box-shadow:
    0 8px 25px rgba(255,45,85,0.4),
    0 4px 15px rgba(75,0,130,0.3),
    inset 0 1px 0 rgba(255,255,255,0.2);
  transform: translateY(-2px) scale(1.05);
  border-color: rgba(255,255,255,0.25);
}

.visitor-details-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
  position: relative;
  z-index: 10;
}

/* Visitor Overview Card */
.visitor-overview-card {
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(5px);
}

.visitor-overview-card h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 1.8rem;
  margin-bottom: 25px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  border-left: 4px solid #FF2D55;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255,255,255,0.1);
}

.overview-label {
  font-weight: 600;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.overview-value {
  font-weight: 700;
  color: #FF2D55;
  font-size: 1.1rem;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Section Statistics Card */
.section-stats-card {
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(5px);
}

.section-stats-card h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 1.8rem;
  margin-bottom: 25px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.section-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.section-stat-item {
  background: rgba(255,255,255,0.05);
  border-radius: 15px;
  padding: 20px;
  border-left: 4px solid #4B0082;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255,255,255,0.1);
}

.section-stat-item h3 {
  color: #fff;
  font-size: 1.3rem;
  margin-bottom: 15px;
  font-weight: 700;
  text-transform: capitalize;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.section-stat-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-detail {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.stat-detail svg {
  color: #FF2D55;
  font-size: 0.9rem;
}

/* Visit History Card */
.visit-history-card {
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(5px);
}

.visit-history-card h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 1.8rem;
  margin-bottom: 25px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.visit-history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 500px;
  overflow-y: auto;
}

.visit-history-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 20px;
  padding: 15px 20px;
  background: rgba(255,255,255,0.05);
  border-radius: 10px;
  border-left: 3px solid #FF2D55;
  align-items: center;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255,255,255,0.1);
}

.visit-timestamp {
  font-weight: 600;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.visit-section {
  font-weight: 500;
  color: #fff;
  text-transform: capitalize;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.visit-duration {
  font-weight: 700;
  color: #FF2D55;
  text-align: right;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Loading and Error States */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.error-message {
  background: rgba(255, 255, 255, 0.95);
  color: #e74c3c;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 20px;
  font-weight: 600;
  text-align: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .visitor-details-container {
    margin: 15px auto;
    padding: 25px 15px;
    border-radius: 20px;
    overflow: hidden;
    max-width: calc(100vw - 30px);
    width: calc(100vw - 30px);
    box-sizing: border-box;
    display: block;
    position: relative;
  }

  .visitor-details-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .visitor-details-header h1 {
    font-size: 2rem;
  }

  .back-button {
    padding: 10px 16px;
    font-size: 0.9rem;
    border-radius: 20px;
    gap: 6px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .section-stats-grid {
    grid-template-columns: 1fr;
  }

  .visit-history-item {
    grid-template-columns: 1fr;
    gap: 10px;
    text-align: center;
  }

  .visit-duration {
    text-align: center;
  }

  .overview-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .visitor-details-container {
    margin: 10px auto;
    padding: 20px 10px;
    border-radius: 15px;
    overflow: hidden;
    max-width: calc(100vw - 20px);
    width: calc(100vw - 20px);
    box-sizing: border-box;
    display: block;
    position: relative;
  }

  .visitor-details-header h1 {
    font-size: 1.6rem;
    margin-bottom: 20px;
  }

  .back-button {
    padding: 8px 12px;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
  }

  .visitor-overview-card,
  .section-stats-card,
  .visit-history-card {
    padding: 20px;
    border-radius: 15px;
  }
}
