<!DOCTYPE html>
<html>
<head>
    <title>Final Comprehensive Test - All Visitors Details</title>
    <meta property="og:image" content="https://aminos555.github.io/Porfolio-Pro/logo.PNG" />
    <meta property="og:url" content="https://aminos555.github.io/Porfolio-Pro/" />
    <meta name="twitter:image" content="https://aminos555.github.io/Porfolio-Pro/logo.PNG" />
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
        }
        .test-section { 
            margin: 25px 0; 
            padding: 20px; 
            border: 1px solid rgba(255,255,255,0.2); 
            border-radius: 10px; 
            background: rgba(255,255,255,0.05);
        }
        .success { color: #4ade80; font-weight: bold; }
        .error { color: #f87171; font-weight: bold; }
        .info { color: #60a5fa; }
        .warning { color: #fbbf24; }
        button { 
            margin: 8px; 
            padding: 12px 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-weight: 600;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .test-result { 
            margin: 15px 0; 
            padding: 15px; 
            border-left: 4px solid #ccc; 
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .test-result.success { border-left-color: #4ade80; }
        .test-result.error { border-left-color: #f87171; }
        .test-result.info { border-left-color: #60a5fa; }
        .test-result.warning { border-left-color: #fbbf24; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4ade80;
        }
        .country-flag {
            font-size: 1.5em;
            margin-right: 10px;
        }
        .visitor-sample {
            background: rgba(255,255,255,0.05);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 3px solid #60a5fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Final Comprehensive Test Suite</h1>
        <h2>All Visitors Details - Complete Functionality Verification</h2>
        
        <div class="test-section">
            <h3>🎯 Test Objectives</h3>
            <ul>
                <li>✅ Remove 20-visit limit (show ALL historical visitors)</li>
                <li>✅ Add "Show All" control with priority over other filters</li>
                <li>✅ Display times in Tunisia timezone</li>
                <li>✅ Fix country flags display (PC & mobile)</li>
                <li>✅ Update social media meta tags</li>
                <li>✅ Comprehensive testing & verification</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 Test Controls</h3>
            <button onclick="runFullTestSuite()">🚀 Run Complete Test Suite</button>
            <button onclick="testVisitorLimitFix()">📊 Test Visitor Limit Fix</button>
            <button onclick="testTimezoneDisplay()">🌍 Test Tunisia Timezone</button>
            <button onclick="testMetaTags()">🏷️ Test Meta Tags</button>
            <button onclick="clearResults()">🧹 Clear Results</button>
        </div>

        <div id="stats-display"></div>
        <div id="results"></div>
    </div>

    <script>
        const API_URL = 'https://porfolio-pro-backend.onrender.com';
        let authToken = null;
        let testResults = [];
        let visitorData = null;

        function addResult(type, title, message, details = '') {
            const result = { 
                type, 
                title, 
                message, 
                details, 
                timestamp: new Date().toLocaleString('en-US', { timeZone: 'Africa/Tunis' })
            };
            testResults.push(result);
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>📊 Test Results</h3>' + 
                testResults.map(result => `
                    <div class="test-result ${result.type}">
                        <strong>[${result.timestamp}] ${result.title}</strong><br>
                        ${result.message}
                        ${result.details ? `<br><small>${result.details}</small>` : ''}
                    </div>
                `).join('');
        }

        function updateStatsDisplay(data) {
            const statsDiv = document.getElementById('stats-display');
            if (!data) return;

            const visitCount = data.visits ? data.visits.length : 0;
            const uniqueVisitors = data.uniqueVisitors || 0;
            const countries = data.visits ? [...new Set(data.visits.map(v => v.country).filter(Boolean))] : [];
            
            statsDiv.innerHTML = `
                <h3>📈 Current Database Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">${visitCount}</div>
                        <div>Total Visits</div>
                        <small>${visitCount > 20 ? '✅ Limit removed!' : '⚠️ Need more data'}</small>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${uniqueVisitors}</div>
                        <div>Unique Visitors</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${countries.length}</div>
                        <div>Countries</div>
                        <small>${countries.slice(0,3).join(', ')}</small>
                    </div>
                </div>
            `;

            // Show sample visitors with timezone formatting
            if (data.visits && data.visits.length > 0) {
                const sampleVisitors = data.visits.slice(0, 5);
                const visitorsHtml = sampleVisitors.map(visit => {
                    const tunisiaTime = new Date(visit.timestamp).toLocaleString('en-US', {
                        timeZone: 'Africa/Tunis',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    
                    return `
                        <div class="visitor-sample">
                            <span class="country-flag">🌍</span>
                            <strong>${visit.ip}</strong> - ${visit.section}
                            <br><small>Tunisia Time: ${tunisiaTime}</small>
                        </div>
                    `;
                }).join('');
                
                statsDiv.innerHTML += `
                    <h4>👥 Sample Visitors (Tunisia Timezone)</h4>
                    ${visitorsHtml}
                `;
            }
        }

        function clearResults() {
            testResults = [];
            updateResultsDisplay();
            document.getElementById('stats-display').innerHTML = '';
        }

        async function authenticateAdmin() {
            if (authToken) return true;
            
            addResult('info', '🔐 Admin Authentication', 'Authenticating with admin credentials...');
            
            try {
                const response = await fetch(`${API_URL}/api/admin/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        email: '<EMAIL>', 
                        password: 'Adminboss' 
                    })
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.token) {
                    authToken = data.token;
                    addResult('success', '✅ Admin Authentication', 'Successfully authenticated');
                    return true;
                } else {
                    addResult('error', '❌ Admin Authentication', 'Authentication failed');
                    return false;
                }
            } catch (error) {
                addResult('error', '❌ Admin Authentication', `Authentication error: ${error.message}`);
                return false;
            }
        }

        async function testVisitorLimitFix() {
            addResult('info', '📊 Visitor Limit Test', 'Testing removal of 20-visit limit...');
            
            if (!await authenticateAdmin()) return false;
            
            try {
                const response = await fetch(`${API_URL}/api/admin/dashboard`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                visitorData = data;
                updateStatsDisplay(data);
                
                const visitCount = data.visits ? data.visits.length : 0;
                
                if (visitCount > 20) {
                    addResult('success', '✅ Visitor Limit Fix', 
                        `SUCCESS: Fetched ${visitCount} visits (old limit was 20)`,
                        'The .limit(20) restriction has been successfully removed from backend');
                } else if (visitCount > 0) {
                    addResult('info', 'ℹ️ Visitor Limit Fix', 
                        `Fetched ${visitCount} visits (limit removed, but need more test data)`,
                        'Fix is applied but database may not have enough historical data yet');
                } else {
                    addResult('warning', '⚠️ Visitor Limit Fix', 
                        'No visitor data found in database',
                        'Cannot test limit fix without visitor data');
                }
                
                return visitCount;
            } catch (error) {
                addResult('error', '❌ Visitor Limit Fix', `Test failed: ${error.message}`);
                return false;
            }
        }

        function testTimezoneDisplay() {
            addResult('info', '🌍 Tunisia Timezone Test', 'Testing timezone formatting...');
            
            try {
                const testDate = new Date();
                const tunisiaTime = testDate.toLocaleString('en-US', {
                    timeZone: 'Africa/Tunis',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const utcTime = testDate.toISOString();
                const localTime = testDate.toLocaleString();
                
                // Test if Tunisia time is different from UTC (it should be)
                const tunisiaDate = new Date(testDate.toLocaleString('en-US', { timeZone: 'Africa/Tunis' }));
                const utcDate = new Date(testDate.toISOString());
                const timeDiff = Math.abs(tunisiaDate.getTime() - utcDate.getTime());
                
                if (timeDiff > 0 || tunisiaTime.includes('AM') || tunisiaTime.includes('PM')) {
                    addResult('success', '✅ Tunisia Timezone', 
                        'Timezone formatting working correctly',
                        `UTC: ${utcTime}<br>Local: ${localTime}<br>Tunisia: ${tunisiaTime}`);
                } else {
                    addResult('warning', '⚠️ Tunisia Timezone', 
                        'Timezone formatting may not be working properly',
                        `All times appear similar: ${tunisiaTime}`);
                }
                
                return true;
            } catch (error) {
                addResult('error', '❌ Tunisia Timezone', `Timezone test failed: ${error.message}`);
                return false;
            }
        }

        function testMetaTags() {
            addResult('info', '🏷️ Meta Tags Test', 'Checking social media meta tags...');
            
            const checks = [
                {
                    selector: 'meta[property="og:image"]',
                    expected: 'aminos555.github.io/Porfolio-Pro/logo.PNG',
                    name: 'Open Graph Image'
                },
                {
                    selector: 'meta[name="twitter:image"]',
                    expected: 'aminos555.github.io/Porfolio-Pro/logo.PNG',
                    name: 'Twitter Image'
                },
                {
                    selector: 'meta[property="og:url"]',
                    expected: 'aminos555.github.io/Porfolio-Pro/',
                    name: 'Open Graph URL'
                }
            ];
            
            let passedChecks = 0;
            let details = [];
            
            checks.forEach(check => {
                const element = document.querySelector(check.selector);
                if (element && element.content.includes(check.expected)) {
                    passedChecks++;
                    details.push(`✅ ${check.name}: ${element.content}`);
                } else {
                    details.push(`❌ ${check.name}: ${element ? element.content : 'Not found'}`);
                }
            });
            
            if (passedChecks === checks.length) {
                addResult('success', '✅ Meta Tags Update', 
                    'All social media meta tags updated correctly',
                    details.join('<br>'));
            } else {
                addResult('error', '❌ Meta Tags Update', 
                    `${passedChecks}/${checks.length} meta tags updated correctly`,
                    details.join('<br>'));
            }
            
            return passedChecks === checks.length;
        }

        async function runFullTestSuite() {
            clearResults();
            addResult('info', '🚀 Full Test Suite', 'Starting comprehensive test suite...');
            
            // Test 1: Visitor Limit Fix (main issue)
            const visitCount = await testVisitorLimitFix();
            
            // Test 2: Timezone Display
            testTimezoneDisplay();
            
            // Test 3: Meta Tags
            testMetaTags();
            
            // Final Summary
            const successCount = testResults.filter(r => r.type === 'success').length;
            const errorCount = testResults.filter(r => r.type === 'error').length;
            const warningCount = testResults.filter(r => r.type === 'warning').length;
            
            if (errorCount === 0 && warningCount === 0) {
                addResult('success', '🎉 TEST SUITE COMPLETE', 
                    `ALL TESTS PASSED! (${successCount} successful tests)`,
                    'All fixes are working correctly. Ready for deployment! 🚀');
            } else if (errorCount === 0) {
                addResult('info', '✅ TEST SUITE COMPLETE', 
                    `Tests mostly passed (${successCount} success, ${warningCount} warnings)`,
                    'Minor issues detected but core functionality is working.');
            } else {
                addResult('error', '⚠️ TEST SUITE COMPLETE', 
                    `Some tests failed (${successCount} success, ${errorCount} errors, ${warningCount} warnings)`,
                    'Issues need to be addressed before deployment.');
            }
        }

        // Auto-run tests when page loads
        window.onload = () => {
            setTimeout(() => {
                addResult('info', '🔄 Auto-Start', 'Starting comprehensive test suite...');
                setTimeout(runFullTestSuite, 2000);
            }, 1000);
        };
    </script>
</body>
</html>
