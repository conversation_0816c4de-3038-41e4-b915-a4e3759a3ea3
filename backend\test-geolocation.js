require('dotenv').config();
const { batchGetCountriesFromIPs } = require('./utils/geolocation');

async function testGeolocation() {
  console.log('🧪 TESTING BACKEND GEOLOCATION SERVICE');
  console.log('=====================================\n');
  
  // Test IPs (mix of real and local)
  const testIPs = [
    '*******',           // Google DNS
    '*******',           // Cloudflare DNS
    '127.0.0.1',         // Localhost
    '::1',               // IPv6 localhost
    '***********',       // Private IP
    '*************',     // Real IP from your logs
    '**************'     // Another real IP
  ];
  
  console.log('🎯 Test IPs:', testIPs);
  console.log('\n🚀 Starting geolocation lookup...\n');
  
  try {
    const results = await batchGetCountriesFromIPs(testIPs);
    
    console.log('\n📊 RESULTS:');
    console.log('===========');
    
    Object.entries(results).forEach(([ip, data]) => {
      const status = data.error ? '❌' : '✅';
      const location = data.error 
        ? `Error: ${data.errorMessage}` 
        : `${data.country}, ${data.city} ${data.flag}`;
      
      console.log(`${status} ${ip.padEnd(15)} → ${location}`);
    });
    
    // Summary
    const totalIPs = Object.keys(results).length;
    const successfulLookups = Object.values(results).filter(data => !data.error).length;
    const failedLookups = totalIPs - successfulLookups;
    
    console.log('\n📈 SUMMARY:');
    console.log('===========');
    console.log(`✅ Successful lookups: ${successfulLookups}/${totalIPs}`);
    console.log(`❌ Failed lookups: ${failedLookups}/${totalIPs}`);
    console.log(`📊 Success rate: ${Math.round((successfulLookups / totalIPs) * 100)}%`);
    
    if (successfulLookups > 0) {
      console.log('\n🎉 Backend geolocation service is working!');
    } else {
      console.log('\n⚠️ All geolocation lookups failed - check API connectivity');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testGeolocation();
