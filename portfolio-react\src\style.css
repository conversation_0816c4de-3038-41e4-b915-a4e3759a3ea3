/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    background:
        linear-gradient(135deg, rgba(75,0,130,0.2) 0%, rgba(255,45,85,0.15) 100%),
        radial-gradient(circle at 20% 20%, rgba(75,0,130,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255,45,85,0.1) 0%, transparent 50%),
        #000000;
    color: #FFFFFF;
    line-height: 1.6;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    position: relative;
    min-height: 100vh;
}

/* Add floating particles effect to body */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255,45,85,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(75,0,130,0.1) 1px, transparent 1px);
    background-size: 50px 50px, 30px 30px;
    animation: floatingParticles 20s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes floatingParticles {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-20px) rotate(360deg); }
}

/* Header Styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    width: 100%;
    top: 0;
    z-index: 100;
    box-sizing: border-box;
}

/* Mobile devices (screen width <= 768px) - Header does NOT follow scroll, logo left, CV button right */
@media (max-width: 768px) {
    header {
        position: relative; /* Scrolls with the page */
        display: flex !important; /* Force flex display */
        flex-direction: row !important; /* Force horizontal layout */
        justify-content: space-between !important; /* Distribute items */
        align-items: center !important; /* Vertically center items */
        flex-wrap: nowrap !important; /* Prevent wrapping */
        min-height: 60px; /* Ensure enough height */
        padding: 10px 20px; /* Reduced padding for more space */
        width: 100% !important; /* Ensure full width */
        overflow: hidden; /* Prevent overflow issues */
    }
    .logo {
        margin-right: auto; /* Pushes logo to the left */
        display: flex !important; /* Ensure logo behaves as a flex item */
        align-items: center !important; /* Center logo vertically */
        max-width: 40%; /* Limit logo width to prevent overflow */
    }
    .logo-img {
        max-width: 50px !important; /* Force logo size */
        height: auto !important;
    }
    .cv-button {
        margin-left: auto; /* Pushes CV button to the right */
        white-space: nowrap !important; /* Prevent text wrapping */
        max-width: 40%; /* Limit button width to prevent overflow */
        padding: 10px 20px !important; /* Increased padding for better fit */
        font-size: 14px !important; /* Ensure readable text size */
        text-align: center; /* Center text in button */
        display: inline-block; /* Ensure button behaves inline */
        overflow: hidden; /* Prevent text overflow */
        text-overflow: ellipsis; /* Add ellipsis if text overflows (optional) */
        box-sizing: border-box !important; /* Include padding in width calculation */
    }
}

/* Desktop devices (screen width > 768px) - Header follows scroll */
@media (min-width: 769px) {
    header {
        position: fixed; /* Stays visible and follows the user */
    }
    body {
        padding-top: 80px; /* Prevents content from hiding under the fixed header */
    }
}

.logo img {
    height: 76px;
    width: 76px;
    border-radius: 50%;
    border: 3px solid #4B0082;
    box-shadow: 0 4px 15px rgba(255, 45, 85, 0.3);
    transition: transform 0.3s ease;
    object-fit: cover;
    object-position: center top;
}

.logo img:hover {
    transform: scale(1.1);
}

.cv-button {
    background-color: #4B0082;
    color: #FFFFFF;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 700;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
}

.cv-button:hover {
    background-color: #FFFFFF;
    color: #4B0082;
}

/* Intro Section */
.intro {
    text-align: center;
    padding: 100px 20px;
    margin-top: 80px;
}

.intro h1 {
    font-size: 48px;
    font-weight: 700;
    letter-spacing: 2px;
}

.intro .highlight {
    color: #4B0082;
}

.intro p {
    font-size: 18px;
    margin: 20px 0;
    text-transform: uppercase;
}

/* Combined Intro-Image and Crafting Section */
.intro-crafting {
    padding: 80px 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-areas:
        "content image";
    align-items: center;
    justify-items: center;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.crafting-content {
    grid-area: content;
    text-align: center;
    max-width: 100%;
}

.crafting-content h2 {
    font-size: 36px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.crafting-content p {
    font-size: 16px;
    max-width: 600px;
    margin: 0 auto 40px;
}

.crafting-content .services-and-skills {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
    max-width: 100%;
    margin: 0 auto;
}

.services {
    display: flex;
    gap: 40px;
}

.service-item {
    text-align: center;
    max-width: 250px;
}

.service-item img {
    height: 50px;
    margin-bottom: 20px;
    border-radius: 50%;
    border: 2px solid #4B0082;
}

.service-item h3 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 10px;
}

.service-item p {
    font-size: 14px;
}

.skills-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    text-transform: uppercase;
    color: #4B0082;
    padding: 10px 0;
}

.intro-image-wrapper {
    grid-area: image;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 100%;
}

.intro-image {
    margin-bottom: 20px;
}

.intro-image img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 50%;
    border: 3px solid #4B0082;
    box-shadow: 0 4px 15px rgba(255, 45, 85, 0.3);
    object-fit: cover;
    object-position: center top; /* This will keep the top part (hair) visible */
    aspect-ratio: 1/1; /* Forces a perfect circle */
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.social-links img {
    height: 30px;
    transition: transform 0.3s ease;
}

.social-links img:hover {
    transform: scale(1.1);
}

/* Responsive Design for Intro-Crafting Section */
@media (max-width: 1024px) {
    .intro-crafting {
        grid-template-columns: 1fr 1fr;
        padding: 60px 15px;
        gap: 30px;
    }
    .intro-image img {
        max-width: 350px;
        object-fit: cover;
        object-position: center top;
        aspect-ratio: 1/1;
    }
    .crafting-content h2 {
        font-size: 32px;
    }
    .crafting-content p {
        font-size: 15px;
    }
    .service-item img {
        height: 45px;
    }
    .service-item h3 {
        font-size: 16px;
    }
    .service-item p {
        font-size: 13px;
    }
    .social-links img {
        height: 28px;
    }
    .skills-bar {
        font-size: 13px;
        gap: 8px;
    }
}

@media (max-width: 768px) {
    .intro-crafting {
        grid-template-columns: 1fr;
        grid-template-areas:
            "image"
            "content"; /* Reordered to show image first */
        padding: 40px 10px;
        gap: 20px;
    }
    .intro-image img {
        max-width: 300px;
        object-fit: cover;
        object-position: center top;
        aspect-ratio: 1/1;
    }
    .crafting-content h2 {
        font-size: 28px;
    }
    .crafting-content p {
        font-size: 14px;
    }
    .crafting-content .services-and-skills {
        flex-direction: column;
        align-items: center;
    }
    .services {
        flex-direction: column;
        gap: 20px;
    }
    .service-item {
        max-width: 100%;
    }
    .service-item img {
        height: 40px;
    }
    .service-item h3 {
        font-size: 15px;
    }
    .service-item p {
        font-size: 12px;
    }
    .social-links img {
        height: 25px;
    }
    .skills-bar {
        font-size: 12px;
        gap: 6px;
    }
    .timeline-content .company-link {
        word-break: break-all;
        overflow-wrap: anywhere;
        display: block;
        margin-bottom: 10px;
        text-align: left;
        font-size: 13px;
        padding-right: 0;
        padding-left: 0;
        width: 100%;
    }
    .timeline-item:nth-child(even) .timeline-content .company-link {
        text-align: right;
    }
}

@media (max-width: 480px) {
    .intro-crafting {
        padding: 30px 10px;
        gap: 15px;
    }
    .intro-image img {
        max-width: 250px;
        object-fit: cover;
        object-position: center top;
        aspect-ratio: 1/1;
    }
    .crafting-content h2 {
        font-size: 24px;
    }
    .crafting-content p {
        font-size: 13px;
    }
    .service-item img {
        height: 35px;
    }
    .service-item h3 {
        font-size: 14px;
    }
    .service-item p {
        font-size: 11px;
    }
    .social-links img {
        height: 20px;
    }
    .social-links {
        gap: 15px;
    }
    .skills-bar {
        font-size: 11px;
        gap: 5px;
        flex-wrap: wrap;
    }
    .timeline-content .company-link {
        font-size: 12px;
        margin-bottom: 8px;
        padding-right: 0;
        padding-left: 0;
        width: 100%;
        word-break: break-all;
        overflow-wrap: anywhere;
        display: block;
    }
    .timeline-item:nth-child(even) .timeline-content .company-link {
        text-align: right;
    }
}

/* Experience Timeline Section */
.experience {
    padding: 80px 20px;
    text-align: center;
    position: relative;
}

.experience h2 {
    font-size: 36px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 60px;
    color: #FFFFFF;
}

.timeline {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #4B0082, #FF2D55, #4B0082);
    transform: translateX(-50%);
    z-index: 1;
}

.timeline-item {
    position: relative;
    margin: 60px 0;
    opacity: 0;
    animation: fadeInUp 0.8s ease forwards;
}

.timeline-item:nth-child(1) { animation-delay: 0.2s; }
.timeline-item:nth-child(2) { animation-delay: 0.4s; }
.timeline-item:nth-child(3) { animation-delay: 0.6s; }
.timeline-item:nth-child(4) { animation-delay: 0.8s; }

.timeline-item:nth-child(odd) .timeline-content {
    margin-left: 60%;
    text-align: left;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-right: 60%;
    text-align: right;
}

.timeline-dot {
    position: absolute;
    left: 50%;
    top: 20px;
    width: 20px;
    height: 20px;
    background: #4B0082;
    border: 4px solid #FFFFFF;
    border-radius: 50%;
    transform: translateX(-50%);
    z-index: 2;
    transition: all 0.3s ease;
}

.timeline-item:hover .timeline-dot {
    background: #FF2D55;
    transform: translateX(-50%) scale(1.3);
    box-shadow: 0 0 20px rgba(255, 45, 85, 0.5);
}

.timeline-content {
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(75, 0, 130, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);
}

/* Timeline Content Link Styles */
.timeline-content-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: all 0.3s ease;
}

.timeline-content-link:hover {
    text-decoration: none;
    color: inherit;
}

.view-details {
    margin-top: 15px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.view-details span {
    color: #FF2D55;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.timeline-content-link:hover .view-details {
    opacity: 1;
    transform: translateY(0);
}

.timeline-content-link:hover .view-details span {
    color: #FFFFFF;
}

.company-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #4B0082;
    margin-bottom: 20px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.timeline-item:hover .company-logo {
    border-color: #FF2D55;
    transform: scale(1.1);
}

.job-title {
    font-size: 20px;
    font-weight: 700;
    color: #4B0082;
    text-transform: uppercase;
    margin-bottom: 10px;
    transition: color 0.3s ease;
}

.timeline-item:hover .job-title {
    color: #FF2D55;
}

.company-name {
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
    margin-bottom: 10px;
}

.job-duration {
    font-size: 14px;
    color: #FF2D55;
    text-transform: uppercase;
    margin-bottom: 20px;
    font-weight: 600;
}

.job-description {
    font-size: 14px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Other Sections Remain Unchanged */
.statistics {
    padding: 80px 20px;
    text-align: center;
}

.statistics h2 {
    font-size: 24px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 40px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.stat h3 {
    font-size: 36px;
    font-weight: 700;
    color: #4B0082;
}

.stat p {
    font-size: 14px;
    text-transform: uppercase;
}

.stats-image img {
    width: 100%;
    max-width: 600px;
    height: auto;
    margin: 40px 0;
}

.action-button {
    display: inline-block;
    background-color: #4B0082;
    color: #FFFFFF;
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-weight: 700;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.action-button:hover {
    background-color: #FFFFFF;
    color: #4B0082;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(75, 0, 130, 0.3);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(75, 0, 130, 0.2);
}

/* Portfolio Section */
.portfolio {
    padding: 80px 20px;
    text-align: center;
}

.portfolio h2 {
    font-size: 36px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.discover-button {
    display: inline-block;
    background-color: #4B0082;
    color: #FFFFFF;
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 40px;
    transition: background-color 0.3s ease;
}

.discover-button:hover {
    background-color: #FFFFFF;
    color: #4B0082;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.portfolio-item img {
    width: 100%;
    height: auto;
}

.portfolio-item p {
    font-size: 18px;
    font-weight: 700;
    margin: 10px 0;
}

.portfolio-item span {
    font-size: 14px;
    color: #4B0082;
    text-transform: uppercase;
}

.portfolio-item a {
    display: block; /* Makes the entire item clickable */
    text-decoration: none; /* Removes underline from link */
    color: inherit; /* Inherits text color */
}

.portfolio-item a:hover {
    opacity: 0.8; /* Optional: Add hover effect */
}

/* Client Thoughts Section */
.client-thoughts {
    padding: 80px 20px;
    text-align: center;
}

.quote-icon img {
    height: 40px;
    margin-bottom: 20px;
}

.client-thoughts h2 {
    font-size: 24px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.client-thoughts h3 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.client-thoughts p {
    font-size: 16px;
    max-width: 600px;
    margin: 0 auto 20px;
}

.client-name {
    font-size: 14px;
    text-transform: uppercase;
    color: #4B0082;
}

.client-logos {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
}

.client-logos img {
    height: 50px;
}

.client-thoughts .quote-icon img {
    width: 120px; /* Larger size, overriding inline style if needed */
    height: auto;
    transition: transform 0.3s ease; /* Smooth hover effect */
    border-radius: 50%; /* Circular shape for a modern, attractive look */
    box-shadow: 0 4px 8px rgba(255, 45, 85, 0.5); /* Pink shadow for depth */
    margin-bottom: 20px; /* Space below the image */
}

.client-thoughts .quote-icon img:hover {
    transform: scale(1.1); /* Slight zoom on hover for interactivity */
}

.client-thoughts .thoughts-image img {
    box-shadow: 0 4px 8px rgba(255, 45, 85, 0.5);
    transition: transform 0.3s ease;
}

.client-thoughts .thoughts-image img:hover {
    transform: scale(1.05);
}

/* Contact Section */
.contact {
    position: relative;
    min-height: 100vh; /* Full viewport height */
    padding: 80px 20px;
    background: url('./contact01.png') no-repeat center center/contain; /* Background image for desktop */
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two equal columns on desktop */
    grid-template-areas:
        "overlay image"; /* Define areas */
    overflow: hidden;
    align-items: center; /* Vertically center content */
}

.contact-overlay {
    grid-area: overlay; /* Position on the left */
    z-index: 1; /* Ensure content is above the background */
    padding: 40px;
    border-radius: 10px 0 0 10px; /* Rounded corners on the left side */
    max-width: 500px; /* Limit width to ensure all content fits */
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5); /* Optional: Semi-transparent background for contrast on desktop */
}

.contact h2 {
    font-size: 48px; /* Bold, large title */
    font-weight: 700;
    text-transform: uppercase;
    color: #FFFFFF;
    margin-bottom: 40px;
    letter-spacing: 2px;
}

.contact label {
    display: block;
    font-size: 12px; /* Smaller labels */
    text-transform: uppercase;
    color: #FFFFFF;
    margin-bottom: 5px;
    text-align: left;
    opacity: 0.8;
}

.contact input,
.contact textarea {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
    background-color: rgba(255, 255, 255, 0.1); /* Transparent white */
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #FFFFFF;
    font-size: 14px;
    border-radius: 5px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: border-color 0.3s ease;
}

.contact input:focus,
.contact textarea:focus {
    border-color: #4B0082; /* Pink focus outline */
    outline: none;
}

.contact textarea {
    height: 120px; /* Adjusted height */
    resize: none;
}

.contact input::placeholder,
.contact textarea::placeholder {
    color: #CCCCCC; /* Light gray placeholders */
    opacity: 0.7;
}

.submit-button {
    background-color: #4B0082;
    color: #FFFFFF;
    border: none;
    padding: 0;
    border-radius: 50%;
    font-weight: 700;
    text-transform: uppercase;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    width: 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px auto 0;
    font-size: 16px;
}

.submit-button:hover {
    background-color: #FFFFFF;
    color: #4B0082;
    transform: scale(1.1); /* Slight zoom */
}

/* Responsive Design for Contact Section */
@media (max-width: 1024px) {
    .contact {
        grid-template-columns: 1fr 1fr; /* Keep two columns but adjust sizes */
        padding: 60px 15px;
    }
    .contact-overlay {
        max-width: 450px;
        padding: 30px;
    }
    .contact h2 {
        font-size: 40px;
    }
    .contact input,
    .contact textarea {
        padding: 10px;
        font-size: 13px;
    }
    .submit-button {
        width: 70px;
        height: 70px;
        font-size: 15px;
    }
}

@media (max-width: 768px) {
    .contact {
        grid-template-columns: 1fr; /* Stack on smaller screens */
        grid-template-areas:
            "overlay"; /* Only overlay area */
        padding: 40px 10px;
        min-height: auto; /* Allow content to dictate height */

        background-image: none; /* Explicitly remove background image */
    }
    .contact-overlay {
        max-width: 400px;
        padding: 20px;
        border-radius: 10px; /* Full border radius on mobile */
        margin: 0 auto;
        background: rgba(0, 0, 0, 0.8); /* Darker overlay for mobile */
    }
    .contact h2 {
        font-size: 36px;
    }
    .contact label {
        font-size: 11px;
    }
    .contact input,
    .contact textarea {
        padding: 8px;
        font-size: 12px;
    }
    .contact textarea {
        height: 100px;
    }
    .submit-button {
        width: 60px;
        height: 60px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .contact {
        padding: 30px 10px;
    }
    .contact-overlay {
        max-width: 300px;
        padding: 15px;
    }
    .contact h2 {
        font-size: 28px;
        margin-bottom: 30px;
    }
    .contact label {
        font-size: 10px;
    }
    .contact input,
    .contact textarea {
        padding: 6px;
        font-size: 11px;
        margin-bottom: 15px;
    }
    .contact textarea {
        height: 80px;
    }
    .submit-button {
        width: 50px;
        height: 50px;
        font-size: 12px;
        margin-top: 15px;
    }
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    font-size: 14px;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #4B0082;
    color: #FFFFFF;
    font-size: 24px;
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.back-to-top:hover {
    background-color: #FFFFFF;
    color: #4B0082;
}

/* Experience Section Responsive Design */
@media (max-width: 1024px) {
    .experience {
        padding: 60px 15px;
    }

    .timeline-item:nth-child(odd) .timeline-content {
        margin-left: 55%;
    }

    .timeline-item:nth-child(even) .timeline-content {
        margin-right: 55%;
    }

    .timeline-content {
        padding: 25px;
    }

    .job-title {
        font-size: 18px;
    }

    .company-name {
        font-size: 16px;
    }
}

@media (max-width: 768px) {
    .experience {
        padding: 40px 15px;
    }

    .experience h2 {
        font-size: 28px;
        margin-bottom: 40px;
    }

    /* Keep the centered timeline but make it more prominent */
    .timeline::before {
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        background: linear-gradient(180deg, #4B0082, #FF2D55, #4B0082, #FF2D55);
        box-shadow: 0 0 10px rgba(75, 0, 130, 0.3);
    }

    /* Create a zigzag pattern for mobile with wider cards */
    .timeline-item:nth-child(odd) .timeline-content {
        margin-left: 52%;
        margin-right: 2%;
        text-align: left;
        position: relative;
    }

    .timeline-item:nth-child(even) .timeline-content {
        margin-right: 52%;
        margin-left: 2%;
        text-align: right;
        position: relative;
    }

    /* Add connecting lines from dots to cards */
    .timeline-item:nth-child(odd) .timeline-content::before {
        content: '';
        position: absolute;
        left: -18px;
        top: 25px;
        width: 18px;
        height: 2px;
        background: linear-gradient(90deg, #4B0082, #FF2D55);
        border-radius: 2px;
    }

    .timeline-item:nth-child(even) .timeline-content::before {
        content: '';
        position: absolute;
        right: -18px;
        top: 25px;
        width: 18px;
        height: 2px;
        background: linear-gradient(90deg, #FF2D55, #4B0082);
        border-radius: 2px;
    }

    .timeline-dot {
        left: 50%;
        transform: translateX(-50%);
        width: 16px;
        height: 16px;
        border: 3px solid #FFFFFF;
        box-shadow: 0 0 10px rgba(75, 0, 130, 0.4);
    }

    .timeline-item:hover .timeline-dot {
        transform: translateX(-50%) scale(1.4);
        box-shadow: 0 0 20px rgba(255, 45, 85, 0.6);
    }

    .timeline-content {
        padding: 30px 25px;
        border-radius: 20px;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.25);
        box-shadow:
            0 8px 32px rgba(75, 0, 130, 0.25),
            0 4px 16px rgba(255, 45, 85, 0.1);
        min-height: 200px;
    }

    .timeline-content:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 40px rgba(75, 0, 130, 0.35),
            0 10px 25px rgba(255, 45, 85, 0.2);
    }

    /* Mobile timeline link styles */
    .timeline-content-link:hover .timeline-content {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 40px rgba(75, 0, 130, 0.35),
            0 10px 25px rgba(255, 45, 85, 0.2);
    }

    .company-logo {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;
        border: 2px solid #4B0082;
        box-shadow: 0 4px 15px rgba(75, 0, 130, 0.3);
    }

    .timeline-item:hover .company-logo {
        border-color: #FF2D55;
        transform: scale(1.15);
        box-shadow: 0 6px 20px rgba(255, 45, 85, 0.4);
    }

    .job-title {
        font-size: 16px;
        margin-bottom: 8px;
    }

    .company-name {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .job-duration {
        font-size: 12px;
        margin-bottom: 15px;
        font-weight: 700;
    }

    .job-description {
        font-size: 13px;
        line-height: 1.5;
        color: rgba(255, 255, 255, 0.95);
        font-weight: 400;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
    }
}

@media (max-width: 480px) {
    .experience {
        padding: 30px 10px;
    }

    .experience h2 {
        font-size: 24px;
        margin-bottom: 30px;
    }

    .timeline-item {
        margin: 35px 0;
    }

    /* Maintain the alternating pattern with optimized margins for small screens */
    .timeline-item:nth-child(odd) .timeline-content {
        margin-left: 55%;
        margin-right: 2%;
        text-align: left;
    }

    .timeline-item:nth-child(even) .timeline-content {
        margin-right: 55%;
        margin-left: 2%;
        text-align: right;
    }

    /* Adjust connecting lines for smaller screens */
    .timeline-item:nth-child(odd) .timeline-content::before {
        left: -25px;
        width: 25px;
        top: 20px;
    }

    .timeline-item:nth-child(even) .timeline-content::before {
        right: -25px;
        width: 25px;
        top: 20px;
    }

    .timeline::before {
        width: 3px;
        box-shadow: 0 0 8px rgba(75, 0, 130, 0.4);
    }

    .timeline-dot {
        width: 14px;
        height: 14px;
        border: 2px solid #FFFFFF;
    }

    .timeline-item:hover .timeline-dot {
        transform: translateX(-50%) scale(1.3);
    }

    .timeline-content {
        padding: 25px 20px;
        border-radius: 15px;
        min-height: 180px;
    }

    .timeline-content:hover {
        transform: translateY(-6px) scale(1.01);
    }

    /* Small screen timeline link styles */
    .timeline-content-link:hover .timeline-content {
        transform: translateY(-6px) scale(1.01);
    }

    .company-logo {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
        border: 2px solid #4B0082;
    }

    .timeline-item:hover .company-logo {
        transform: scale(1.1);
    }

    .job-title {
        font-size: 14px;
        margin-bottom: 6px;
    }

    .company-name {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .job-duration {
        font-size: 11px;
        margin-bottom: 12px;
        font-weight: 700;
    }

    .job-description {
        font-size: 12px;
        line-height: 1.4;
        color: rgba(255, 255, 255, 0.95);
        font-weight: 400;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .intro h1 {
        font-size: 36px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    header {
        flex-direction: column;
        gap: 20px;
    }

    .intro h1 {
        font-size: 28px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Portfolio Carousel Styles */
.portfolio {
    padding: 80px 20px;
    text-align: center;
}

.portfolio h2 {
    font-size: 36px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.discover-button {
    display: inline-block;
    background-color: #4B0082;
    color: #FFFFFF;
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 40px;
    transition: background-color 0.3s ease;
}

.discover-button:hover {
    background-color: #FFFFFF;
    color: #4B0082;
}

.portfolio-carousel {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
    user-select: none; /* Prevents text selection while dragging */
    /* Ensure wheel events are captured properly */
    pointer-events: auto;
    /* Create a stacking context for proper event handling */
    z-index: 1;
}

.carousel-track {
    display: flex;
    width: 200%; /* Double width to accommodate duplicates */
    overflow-x: auto; /* Allow scrolling for touch devices */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    cursor: grab;
    touch-action: pan-x; /* Allow horizontal panning on touch devices */
    scrollbar-width: none; /* Hide scrollbar on Firefox */
    -ms-overflow-style: none; /* Hide scrollbar on IE/Edge */
    scroll-behavior: auto; /* Disable smooth scrolling for better control */
    user-select: none; /* Prevent text selection during drag */
    transition: none; /* Remove transitions that interfere with dragging */
}

.carousel-track::-webkit-scrollbar {
    display: none; /* Hide scrollbar on WebKit browsers */
}

.carousel-track.dragging {
    cursor: grabbing;
}

/* Improve touch responsiveness */
.carousel-track:active {
    cursor: grabbing;
}

.carousel-track .portfolio-item {
    flex: 0 0 300px;
    margin-right: 40px;
    text-align: center;
    min-width: 300px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    box-shadow:
        0 8px 32px rgba(75, 0, 130, 0.2),
        0 4px 16px rgba(255, 45, 85, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.carousel-track .portfolio-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(75, 0, 130, 0.3),
        0 10px 25px rgba(255, 45, 85, 0.2),
        0 0 20px rgba(75, 0, 130, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.carousel-track .portfolio-item img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    transition: transform 0.3s ease, filter 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.carousel-track .portfolio-item:hover img {
    transform: scale(1.05);
    filter: brightness(1.1) saturate(1.1);
}

.carousel-track .portfolio-item p {
    font-size: 18px;
    font-weight: 700;
    margin: 15px 0 10px;
    text-transform: uppercase;
    transition: color 0.3s ease;
}

.carousel-track .portfolio-item:hover p {
    color: #FF2D55;
    text-shadow: 0 2px 4px rgba(255, 45, 85, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .portfolio-carousel {
        /* Ensure touch events work properly on mobile */
        -webkit-overflow-scrolling: touch;
        touch-action: pan-x;
        /* Improve touch responsiveness */
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .carousel-track {
        width: 200%; /* Maintain double width for infinite loop */
        cursor: default; /* Remove grab cursor on mobile */
        /* Enable native scrolling on mobile */
        overflow-x: auto;
        scroll-behavior: auto; /* Use auto for better touch control */
        /* Improve touch scrolling */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior-x: contain; /* Prevent page scroll when reaching edges */
    }

    .carousel-track .portfolio-item {
        flex: 0 0 200px;
        margin-right: 20px;
        min-width: 200px;
        padding: 10px;
        /* Improve touch target size */
        min-height: 44px;
        /* Prevent touch delay */
        touch-action: manipulation;
    }

    .carousel-track .portfolio-item:hover {
        transform: translateY(-5px) scale(1.01);
    }

    .carousel-track .portfolio-item p {
        font-size: 14px;
        margin: 10px 0 5px;
    }

    /* Disable hover effects on touch devices */
    .carousel-track .portfolio-item:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

/* Skills Ticker Styles */
.skills-ticker {
    width: 100%;
    background-color: #4B0082;
    overflow: hidden;
    padding: 15px 0;
    transform: rotate(3deg);
    transform-origin: center;
    margin: 20px 0;
    position: relative;
}

.ticker-track {
    display: flex;
    white-space: nowrap;
    animation: tickerScroll 30s linear infinite;
}

.ticker-track span {
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    color: #FFFFFF;
    margin: 0 10px;
    transform: rotate(-2deg);
    display: inline-block;
}

/* Animation for scrolling from right to left */
@keyframes tickerScroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Pause animation on hover (optional) */
.ticker-track:hover {
    animation-play-state: paused;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .skills-ticker {
        transform: rotate(3deg);
        padding: 10px 0;
    }
    .ticker-track span {
        transform: rotate(-1deg);
    }
}

/* Infinite Carousel Animation - Now handled by JavaScript */
/* Removed CSS animation to prevent conflicts with JS-based auto-scroll */

/* Admin Login Modal Styles - Redesigned to match portfolio aesthetic */
.admin-modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(75, 0, 130, 0.15) 50%,
    rgba(255, 45, 85, 0.1) 100%);
  backdrop-filter: blur(15px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: overlayFadeIn 0.4s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(15px);
  }
}

.admin-modal {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 25px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 40px rgba(75, 0, 130, 0.35),
    0 10px 25px rgba(255, 45, 85, 0.2),
    0 0 20px rgba(75, 0, 130, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  padding: 50px 40px 40px 40px;
  min-width: 400px;
  max-width: 90vw;
  position: relative;
  color: #fff;
  font-family: 'Montserrat', sans-serif;
  border: 2px solid rgba(75, 0, 130, 0.3);
  animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
}

/* Add a subtle animated background pattern */
.admin-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(75, 0, 130, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 45, 85, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.admin-modal-title {
  text-align: center;
  font-size: 2.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 35px;
  letter-spacing: 2px;
  text-transform: uppercase;
  position: relative;
}

/* Add decorative elements around the title */
.admin-modal-title::before,
.admin-modal-title::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #4B0082, #FF2D55);
  transform: translateY(-50%);
}

.admin-modal-title::before {
  left: -60px;
}

.admin-modal-title::after {
  right: -60px;
}

.admin-modal-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.admin-modal-form label {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.admin-modal-form input {
  padding: 15px 18px;
  border-radius: 15px;
  border: 2px solid rgba(75, 0, 130, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 1rem;
  font-family: 'Montserrat', sans-serif;
  outline: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.admin-modal-form input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.admin-modal-form input:focus {
  border: 2px solid #FF2D55;
  background: rgba(255, 255, 255, 0.15);
  box-shadow:
    0 0 20px rgba(255, 45, 85, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.admin-modal-submit {
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  color: #fff;
  font-weight: 700;
  border: none;
  border-radius: 25px;
  padding: 15px 0;
  font-size: 1.1rem;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 15px;
  cursor: pointer;
  box-shadow:
    0 8px 25px rgba(75, 0, 130, 0.3),
    0 4px 15px rgba(255, 45, 85, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-modal-submit::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.admin-modal-submit:hover::before {
  left: 100%;
}

.admin-modal-submit:hover {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(75, 0, 130, 0.4),
    0 8px 20px rgba(255, 45, 85, 0.3);
}

.admin-modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.4rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow:
    0 4px 15px rgba(255, 45, 85, 0.3),
    0 2px 8px rgba(75, 0, 130, 0.2);
  transition: all 0.3s ease;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.admin-modal-close:hover {
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  transform: scale(1.1) rotate(90deg);
  box-shadow:
    0 6px 20px rgba(255, 45, 85, 0.4),
    0 3px 12px rgba(75, 0, 130, 0.3);
}

@media (max-width: 480px) {
  .admin-modal {
    min-width: 90vw;
    padding: 40px 30px 30px 30px;
    border-radius: 20px;
  }

  .admin-modal-title {
    font-size: 1.6rem;
    margin-bottom: 25px;
  }

  .admin-modal-title::before,
  .admin-modal-title::after {
    display: none;
  }

  .admin-modal-form {
    gap: 20px;
  }

  .admin-modal-form input {
    padding: 12px 15px;
  }

  .admin-modal-submit {
    padding: 12px 0;
    font-size: 1rem;
  }
}