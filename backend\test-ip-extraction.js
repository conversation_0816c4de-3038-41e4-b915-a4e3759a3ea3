async function testIPExtraction() {
  const fetch = (await import('node-fetch')).default;
  console.log('🧪 Testing IP extraction with comma-separated x-forwarded-for header...');
  
  try {
    const response = await fetch('http://localhost:5000/api/track/visit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-forwarded-for': '*******, *******, **********'
      },
      body: JSON.stringify({
        section: 'test-ip-extraction',
        duration: 5,
        sessionId: `test-${Date.now()}`
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Request successful:', result);
      
      // Now check what IP was actually stored
      console.log('\n🔍 Checking what IP was stored in database...');
      
      // We need to check the database to see what IP was actually stored
      const mongoose = require('mongoose');
      await mongoose.connect(process.env.MONGO_URI);
      
      const Visit = mongoose.model('Visit', new mongoose.Schema({
        ip: String,
        section: String,
        duration: Number,
        timestamp: { type: Date, default: Date.now },
        sessionId: String
      }));
      
      const recentVisit = await Visit.findOne({ section: 'test-ip-extraction' }).sort({ timestamp: -1 });
      
      if (recentVisit) {
        console.log(`📊 Stored IP: "${recentVisit.ip}"`);
        
        if (recentVisit.ip.includes(',')) {
          console.log('❌ PROBLEM: IP still contains commas!');
          console.log('🔧 Expected: "*******"');
          console.log(`🔧 Actual: "${recentVisit.ip}"`);
        } else {
          console.log('✅ SUCCESS: IP extraction working correctly!');
        }
      } else {
        console.log('❌ Could not find the test visit in database');
      }
      
      await mongoose.disconnect();
      
    } else {
      console.log('❌ Request failed:', response.status, await response.text());
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }
}

// Load environment variables
require('dotenv').config();

testIPExtraction();
